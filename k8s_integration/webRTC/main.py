import cv2
import asyncio
from aiortc import RTCPeerConnection, RTCSessionDescription, VideoStreamTrack
from fastapi import FastAPI, Request
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from fastapi.middleware.cors import CORSMiddleware


import uvicorn
import json

app = FastAPI()
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 生产环境可改为指定域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)
# app.mount("/static", StaticFiles(directory="static"), name="static")
templates = Jinja2Templates(directory="templates")

# 自定义视频流轨道
class CameraStreamTrack(VideoStreamTrack):
    def __init__(self, rtsp_url=0):
        super().__init__()
        self.cap = cv2.VideoCapture(rtsp_url)
        if not self.cap.isOpened():
            raise RuntimeError("Could not open video source")
        
    async def recv(self):
        pts, time_base = await self.next_timestamp()
        
        ret, frame = self.cap.read()
        if not ret:
            raise RuntimeError("Failed to grab frame")
            
        print("✅ 服务端发送一帧")
        # 转换颜色空间 BGR -> RGB
        frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
        frame = cv2.resize(frame, (640, 480))
        
        # 创建视频帧
        from av import VideoFrame
        video_frame = VideoFrame.from_ndarray(frame, format='rgb24')
        video_frame.pts = pts
        video_frame.time_base = time_base
        
        return video_frame

# WebRTC信令端点
@app.post("/offer")
async def offer(request: Request):
    params = await request.json()
    # print("[DEBUG] Incoming SDP offer:")
    # print(params["sdp"])
    offer = RTCSessionDescription(sdp=params["sdp"], type=params["type"])
    
    pc = RTCPeerConnection()
    
    # 添加本地媒体
    # camera_track = CameraStreamTrack()  # 0表示默认摄像头
    camera_track = CameraStreamTrack("rtsp://***********:8554/stream0")
    pc.addTrack(camera_track)
    
    @pc.on("iceconnectionstatechange")
    async def on_iceconnectionstatechange():
        if pc.iceConnectionState == "failed":
            await pc.close()
    
    # 处理offer
    await pc.setRemoteDescription(offer)
    
    # 创建answer
    answer = await pc.createAnswer()
    await pc.setLocalDescription(answer)
    
    return {"sdp": pc.localDescription.sdp, "type": pc.localDescription.type}

@app.get("/")
async def index(request: Request):
    return templates.TemplateResponse("index.html", {"request": request})

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8080)