import paramiko
import threading
import time

class PersistentSSHClient:
    def __init__(self, host="*************", port=22, username="ubuntu", password="ubuntu", key_filename=None, timeout=5):
        self.host = host
        self.port = port
        self.username = username
        self.password = password
        self.key_filename = key_filename
        self.timeout = timeout
        self.client = None
        self.lock = threading.Lock()  # 多线程安全执行命令

        self.connect()

    def connect(self):
        """初始化 SSH 连接"""
        self.client = paramiko.SSHClient()
        self.client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        self.client.connect(
            hostname=self.host,
            port=self.port,
            username=self.username,
            password=self.password,
            key_filename=self.key_filename,
            timeout=self.timeout
        )
        print(f"[SSH] Connected to {self.host}")

    def execute_command(self, command):
        """执行 SSH 命令"""
        with self.lock:
            if not self.client:
                self.connect()
            stdin, stdout, stderr = self.client.exec_command(command)
            exit_status = stdout.channel.recv_exit_status()  # 等待命令执行完成
            output = stdout.read().decode('utf-8')
            error = stderr.read().decode('utf-8')
            if exit_status != 0:
                raise Exception(f"Command failed with exit status {exit_status}: {error}")
            return output.strip()
        
    def close(self):
        if self.client:
            self.client.close()
            print(f"[SSH] Disconnected from {self.host}")

arm_client = PersistentSSHClient()

arm_client.execute_command("sudo python3 /home/<USER>/Ai_FPV/YDS/yds_1_0.py")  # 示例命令