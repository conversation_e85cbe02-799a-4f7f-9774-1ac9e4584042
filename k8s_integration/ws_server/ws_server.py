# main.py
import redis
import asyncio
import json
import os
import queue
import time
import threading
import paramiko
from fastapi import FastAPI, WebSocket, WebSocketDisconnect, Request
from fastapi.responses import FileResponse, JSONResponse
from fastapi.staticfiles import StaticFiles
import collections
import platform
import socket
import psutil
import subprocess
import base64
from datetime import datetime

# 添加 Kubernetes 客户端
from kubernetes import client, config
import kubernetes.client.exceptions

# 初始化 Kubernetes 客户端
def init_kubernetes():
    try:
        # 尝试从集群内部加载配置
        config.load_incluster_config()
        print("[Kubernetes] 已从集群内部加载配置")
        return True
    except:
        try:
            # 如果不在集群内部，尝试从 kubeconfig 加载
            config.load_kube_config()
            print("[Kubernetes] 已从 kubeconfig 加载配置")
            return True
        except Exception as e:
            print(f"[Kubernetes] 无法加载配置: {e}")
            return False

# 创建 FastAPI 应用
app = FastAPI()

# 挂载静态文件目录
app.mount("/static", StaticFiles(directory="static"), name="static")

# Redis 配置
REDIS_HOST = os.environ.get('REDIS_HOST', 'localhost')
REDIS_PORT = int(os.environ.get('REDIS_PORT', 6379))
REDIS_CHANNELS = ['video_channel_1', 'video_channel_2']

# 全局变量
redis_client = None
redis_pubsub = None
redis_thread = None
redis_connected = False
redis_stats = {
    'video_channel_1': {
        'message_count': 0,
        'detection_count': 0,
        'error_count': 0,
        'last_message_time': None,
        'last_message_time_formatted': '-',
        'last_message_age': None
    },
    'video_channel_2': {
        'message_count': 0,
        'detection_count': 0,
        'error_count': 0,
        'last_message_time': None,
        'last_message_time_formatted': '-',
        'last_message_age': None
    }
}

# 系统启动时间
start_time = time.time()

# 添加回原有的全局变量
#redis_host1 = os.getenv("REDIS_HOST_1", "localhost")
#redis_port1 = int(os.getenv("REDIS_PORT_1", 6379))
arm_host = os.getenv("ARM_HOST", "*************")
#r1 = redis.Redis(host=redis_host1, port=redis_port1, db=0)
task_queue = queue.Queue()
detection_results = {
    "belt1": {
        "status": "waiting", 
        "detect": None, 
        "timestamp": None, 
        "class": "",
        "camera_id": "unknown",
        "model_name": "unknown",
        "detections": [],
        "image": None,
        "perf_info": {}
    },
    "belt2": {
        "status": "waiting", 
        "detect": None, 
        "timestamp": None, 
        "class": "",
        "camera_id": "unknown",
        "model_name": "unknown",
        "detections": [],
        "image": None,
        "perf_info": {}
    }
}

# 添加帧缓存
frame_cache = {
    "belt1": {"v5lite_e_int8": collections.deque(maxlen=5)},
    "belt2": {"v5lite_e_int8": collections.deque(maxlen=5)}
}

# WebSocket 连接管理器
class ConnectionManager:
    def __init__(self):
        self.active_connections = []

    async def connect(self, websocket: WebSocket):
        await websocket.accept()
        self.active_connections.append(websocket)

    def disconnect(self, websocket: WebSocket):
        if websocket in self.active_connections:
            self.active_connections.remove(websocket)

    async def broadcast(self, message: str):
        for connection in self.active_connections:
            try:
                await connection.send_text(message)
            except Exception as e:
                print(f"[WebSocket] 广播消息出错: {e}")

# 初始化 Redis 连接
def init_redis():
    global redis_client, redis_pubsub, redis_connected
    try:
        redis_client = redis.Redis(host=REDIS_HOST, port=REDIS_PORT)
        redis_pubsub = redis_client.pubsub()
        redis_pubsub.subscribe(*REDIS_CHANNELS)
        redis_connected = True
        print(f"[Redis] 连接成功: {REDIS_HOST}:{REDIS_PORT}")
        print(f"[Redis] 开始监听 Redis 频道: {', '.join(REDIS_CHANNELS)}")
        return True
    except Exception as e:
        print(f"[Redis] 连接失败: {e}")
        redis_connected = False
        return False

# 处理 Redis 消息
def handle_redis_message(message):
    if message['type'] != 'message':
        return
    
    channel = message['channel'].decode('utf-8')
    try:
        data = json.loads(message['data'].decode('utf-8'))
        
        # 更新 Redis 统计信息
        redis_stats[channel]['message_count'] += 1
        redis_stats[channel]['last_message_time'] = time.time()
        redis_stats[channel]['last_message_time_formatted'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        # 处理检测结果
        if 'detections' in data:
            redis_stats[channel]['detection_count'] += 1
            
            # 确定是哪个产线的数据
            belt_key = 'belt1' if channel == 'video_channel_1' else 'belt2'
            
            # 提取性能信息
            perf_info = data.get('perf_info', {})
            
            # 提取图像数据（如果有）
            image_data = None
            if 'image' in data and data['image']:
                image_data = data['image']
            
            # 更新检测结果
            detections = data.get('detections', [])
            model = perf_info.get('model_name', '')
            
            # 更新通用字段
            detection_results[belt_key].update({
                'camera_id': data.get('camera_id', 'unknown'),
                'timestamp': data.get('create_timestamp', datetime.now().isoformat()),
                'model_name': model,
                'detections': detections,
                'image': image_data,
                'perf_info': perf_info
            })
            
            # 根据模型类型更新特定字段
            if model == "v5_lite_s":
                if len(detections) > 0:
                    detection_results[belt_key]["status"] = "success"
                    detection_results[belt_key]["detect"] = 1
                    detection_results[belt_key]["class"] = detections[0].get("class", "unknown")
                else:
                    detection_results[belt_key]["status"] = "success"
                    detection_results[belt_key]["detect"] = 0
                    detection_results[belt_key]["class"] = ""
            elif model == "v5_lite_e_int8":
                is_defect = 1 if len(detections) > 0 else 0
                frame_cache[belt_key]["yolov5_lite_e_int8"].append(is_defect)
                detection_results[belt_key]["status"] = "success"
                detection_results[belt_key]["detect"] = is_defect
                if is_defect:
                    detection_results[belt_key]["class"] = detections[0].get("class", "unknown")
                else:
                    if any(frame_cache[belt_key]["yolov5_lite_e_int8"]):
                        detection_results[belt_key]["detect"] = 1
                    else:
                        detection_results[belt_key]["detect"] = 0
            
            print(f"[Redis] 接收到来自 {channel} 的检测结果: {len(detections)} 个检测")
    except Exception as e:
        redis_stats[channel]['error_count'] += 1
        print(f"[Redis] 处理消息出错: {e}")

# Redis 监听线程
def redis_listener(loop):
    """监听 Redis 的 video_channel_1 和 video_channel_2 频道"""
    if not redis_connected or not redis_pubsub:
        print("[Redis] 未连接，无法启动监听线程")
        return
    
    print("[Redis] 启动监听线程")
    while True:
        try:
            message = redis_pubsub.get_message()
            if message and message["type"] == "message":
                channel = message.get('channel', b'unknown').decode('utf-8')
                data = message["data"].decode('utf-8')
                print(f"[Redis] 接收到来自 {channel} 的消息")
                
                # 处理消息
                handle_redis_message(message)
                
                # 广播到 WebSocket
                if channel == "video_channel_1":
                    asyncio.run_coroutine_threadsafe(
                        manager_video1.broadcast(data),
                        loop
                    )
                elif channel == "video_channel_2":
                    asyncio.run_coroutine_threadsafe(
                        manager_video2.broadcast(data),
                        loop
                    )
            time.sleep(0.01)  # 短暂休眠，避免 CPU 占用过高
        except Exception as e:
            print(f"[Redis] 监听线程出错: {e}")
            time.sleep(1)  # 出错后等待一秒再继续

# 启动 Redis 监听线程
def start_redis_listener():
    global redis_thread
    if redis_thread is None or not redis_thread.is_alive():
        redis_thread = threading.Thread(target=redis_listener, daemon=True)
        redis_thread.start()
        print("[Redis] 监听线程已启动")

# 添加CPU使用率平均值计算
cpu_usage_history = []
MAX_HISTORY_SIZE = 5  # 保存最近5个采样点

# 获取系统信息
def get_system_info():
    global cpu_usage_history
    hostname = socket.gethostname()
    os_info = f"{platform.system()} {platform.release()}"
    uptime_seconds = time.time() - start_time
    
    # 格式化运行时间
    days = int(uptime_seconds // 86400)
    hours = int((uptime_seconds % 86400) // 3600)
    minutes = int((uptime_seconds % 3600) // 60)
    
    if days > 0:
        uptime = f"{days}天 {hours}小时 {minutes}分钟"
    elif hours > 0:
        uptime = f"{hours}小时 {minutes}分钟"
    else:
        uptime = f"{minutes}分钟"
    
    # 获取 CPU 和内存使用率 - 使用更长的间隔
    cpu_percent = psutil.cpu_percent(interval=1.0)  # 增加到1秒
    
    # 添加到历史记录并保持最大长度
    cpu_usage_history.append(cpu_percent)
    if len(cpu_usage_history) > MAX_HISTORY_SIZE:
        cpu_usage_history.pop(0)
    
    # 计算平均值
    avg_cpu_percent = sum(cpu_usage_history) / len(cpu_usage_history)
    
    memory_percent = psutil.virtual_memory().percent
    
    # 获取磁盘使用率
    disk_percent = None
    try:
        disk_percent = psutil.disk_usage('/').percent
    except:
        pass
    
    return {
        "hostname": hostname,
        "os_info": os_info,
        "uptime": uptime,
        "uptime_seconds": uptime_seconds,
        "cpu_percent": avg_cpu_percent,  # 使用平均值
        "cpu_current": cpu_percent,      # 保留当前值以便需要
        "memory_percent": memory_percent,
        "disk_percent": disk_percent,
        "timestamp": datetime.now().isoformat()
    }

# 获取容器状态
def get_container_info():
    containers = {
        "web_ui": "unknown",
        "processor": "unknown",
        "processor2": "unknown",
        "redis": "unknown",
        "camera_client": "unknown",
        "camera_client2": "unknown",
        "openplc": "unknown",
        "openplc2": "unknown"
    }
    
    # 存储容器运行节点
    container_nodes = {
        "web_ui": "unknown",
        "processor": "unknown",
        "processor2": "unknown",
        "redis": "unknown",
        "camera_client": "unknown",
        "camera_client2": "unknown",
        "openplc": "unknown",
        "openplc2": "unknown"
    }
    
    # 存储容器运行时间（秒）
    container_uptimes = {
        "web_ui": 0,
        "processor": 0,
        "processor2": 0,
        "redis": 0,
        "camera_client": 0,
        "camera_client2": 0,
        "openplc": 0,
        "openplc2": 0
    }
    
    try:
        # 创建 Kubernetes API 客户端
        v1 = client.CoreV1Api()
        apps_v1 = client.AppsV1Api()
        
        # 获取所有命名空间
        namespaces = v1.list_namespace()
        
        # 定义容器名称映射
        container_name_map = {
            "ws-video-gateway": "web_ui",
            "processor-server": "processor",
            "processor-server-2": "processor2",
            "redis": "redis",
            "image-capture": "camera_client",
            "image-capture-2": "camera_client2",
            "control-belt": "openplc",
            "control-belt-2": "openplc2"
        }
        
        # 遍历所有命名空间
        for ns in namespaces.items:
            namespace = ns.metadata.name
            
            # 获取该命名空间下的所有 Pod
            pods = v1.list_namespaced_pod(namespace)
            
            for pod in pods.items:
                pod_name = pod.metadata.name.lower()
                node_name = pod.spec.node_name if pod.spec.node_name else "unknown"
                
                # 检查 Pod 状态
                pod_status = pod.status.phase
                pod_ready = False
                
                if pod.status.container_statuses:
                    for container_status in pod.status.container_statuses:
                        if container_status.ready:
                            pod_ready = True
                            break
                
                # 计算运行时间
                start_time = pod.status.start_time
                uptime_seconds = 0
                if start_time:
                    start_time_timestamp = start_time.timestamp()
                    uptime_seconds = int(time.time() - start_time_timestamp)
                
                # 根据 Pod 名称匹配我们的服务
                for key, value in container_name_map.items():
                    if key in pod_name:
                        # 特殊处理带有数字的服务名称
                        if key == "processor-server" and "processor-server-2" in pod_name:
                            containers["processor2"] = "running" if pod_status == "Running" and pod_ready else "stopped"
                            container_nodes["processor2"] = node_name
                            container_uptimes["processor2"] = uptime_seconds
                        elif key == "image-capture" and "image-capture-2" in pod_name:
                            containers["camera_client2"] = "running" if pod_status == "Running" and pod_ready else "stopped"
                            container_nodes["camera_client2"] = node_name
                            container_uptimes["camera_client2"] = uptime_seconds
                        elif key == "control-belt" and "control-belt-2" in pod_name:
                            containers["openplc2"] = "running" if pod_status == "Running" and pod_ready else "stopped"
                            container_nodes["openplc2"] = node_name
                            container_uptimes["openplc2"] = uptime_seconds
                        else:
                            containers[value] = "running" if pod_status == "Running" and pod_ready else "stopped"
                            container_nodes[value] = node_name
                            container_uptimes[value] = uptime_seconds
            
            # 获取该命名空间下的所有 Deployment
            try:
                deployments = apps_v1.list_namespaced_deployment(namespace)
                
                for deployment in deployments.items:
                    deployment_name = deployment.metadata.name.lower()
                    
                    # 检查 Deployment 状态
                    deployment_ready = False
                    if deployment.status.ready_replicas and deployment.status.ready_replicas > 0:
                        deployment_ready = True
                    
                    # 根据 Deployment 名称匹配我们的服务
                    for key, value in container_name_map.items():
                        if key in deployment_name:
                            # 特殊处理带有数字的服务名称
                            if key == "processor-server" and "processor-server-2" in deployment_name:
                                containers["processor2"] = "running" if deployment_ready else "stopped"
                            elif key == "image-capture" and "image-capture-2" in deployment_name:
                                containers["camera_client2"] = "running" if deployment_ready else "stopped"
                            elif key == "control-belt" and "control-belt-2" in deployment_name:
                                containers["openplc2"] = "running" if deployment_ready else "stopped"
                            else:
                                containers[value] = "running" if deployment_ready else "stopped"
            except Exception as e:
                print(f"获取 Deployment 信息出错: {e}")
        
        # 打印调试信息
        print(f"容器状态: {containers}")
        print(f"容器节点: {container_nodes}")
        print(f"容器运行时间: {container_uptimes}")
    
    except Exception as e:
        print(f"获取 Kubernetes 信息出错: {e}")
    
    return {
        "containers": containers,
        "container_nodes": container_nodes,
        "container_uptimes": container_uptimes
    }

# SSH客户端：连接机械臂
class PersistentSSHClient:
    def __init__(self, host="*************", port=22, username="ubuntu", password="ubuntu", key_filename=None, timeout=5):
        self.host = host
        self.port = port
        self.username = username
        self.password = password
        self.key_filename = key_filename
        self.timeout = timeout
        self.client = None
        self.lock = threading.Lock()  # 多线程安全执行命令

        try:
            self.connect()
        except Exception as e:
            print(f"[SSH] 连接失败: {e}")

    def connect(self):
        """初始化 SSH 连接"""
        self.client = paramiko.SSHClient()
        self.client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        self.client.connect(
            hostname=self.host,
            port=self.port,
            username=self.username,
            password=self.password,
            key_filename=self.key_filename,
            timeout=self.timeout
        )
        print(f"[SSH] Connected to {self.host}")

    def execute_command(self, command):
        """执行 SSH 命令"""
        with self.lock:
            if not self.client:
                self.connect()
            stdin, stdout, stderr = self.client.exec_command(command)
            exit_status = stdout.channel.recv_exit_status()  # 等待命令执行完成
            output = stdout.read().decode('utf-8')
            error = stderr.read().decode('utf-8')
            if exit_status != 0:
                raise Exception(f"Command failed with exit status {exit_status}: {error}")
            return output.strip()
        
    def close(self):
        if self.client:
            self.client.close()
            print(f"[SSH] Disconnected from {self.host}")

# 初始化连接管理器和SSH客户端
manager_video1 = ConnectionManager()
manager_video2 = ConnectionManager()
arm_client = PersistentSSHClient(host=arm_host)

# SSH任务监听线程
def ssh_task_listener():
    class_dict = {
        "bruise": "0",
        "laps": "1",
        "scratches_1": "2",
        "scratches_2": "2"
    }
    while True:
        if not task_queue.empty():
            task = task_queue.get()
            belt = task.get("belt")
            belt_n = belt.split("belt")[-1]  # 获取带子编号
            if belt == "belt1":
                belt_n = "1"
            elif belt == "belt2":
                belt_n = "2"

            class_name = task.get("class")
            class_n = class_dict.get(class_name, "0")  # 默认使用 bruise 类别
            # 这里使用 paramiko 发送 SSH 命令
            try:
                # ssh
                print(f"[SSH] 发送命令: sudo python3 /home/<USER>/Ai_FPV/YDS/yds_{belt_n}_{class_n}.py")
                arm_client.execute_command(f"sudo python3 /home/<USER>/Ai_FPV/YDS/yds_{belt_n}_{class_n}.py")
            except Exception as e:
                print(f"[SSH] 执行失败: {e}")
        time.sleep(0.1)  # 每秒检查一次任务队列

# 路由：主页
@app.get("/")
async def root():
    return {"message": "WebSocket Server API"}

# 路由：仪表盘页面
@app.get("/dashboard")
async def dashboard():
    return FileResponse("static/dashboard.html")

# API：获取检测结果
@app.get("/api/detection_results")
async def get_detection_results():
    return detection_results

# API：获取 Redis 统计信息
@app.get("/api/redis_stats")
async def get_redis_stats():
    # 计算最后消息的时间差
    current_time = time.time()
    for channel in redis_stats:
        if redis_stats[channel]['last_message_time']:
            redis_stats[channel]['last_message_age'] = int(current_time - redis_stats[channel]['last_message_time'])
        else:
            redis_stats[channel]['last_message_age'] = None
    
    return {"stats": redis_stats}

# API：获取系统信息
@app.get("/api/system_info")
async def api_system_info():
    return get_system_info()

# API：获取容器信息
@app.get("/api/container_info")
async def api_container_info():
    return get_container_info()

# WebSocket 端点
@app.websocket("/ws/video1")
async def websocket_video1(websocket: WebSocket):
    await manager_video1.connect(websocket)
    try:
        while True:
            await websocket.receive_text()
    except Exception as e:
        print(f"[video1] WebSocket error: {e}")
    finally:
        manager_video1.disconnect(websocket)

@app.websocket("/ws/video2")
async def websocket_video2(websocket: WebSocket):
    await manager_video2.connect(websocket)
    try:
        while True:
            await websocket.receive_text()
    except Exception as e:
        print(f"[video2] WebSocket error: {e}")
    finally:
        manager_video2.disconnect(websocket)

# 添加新的 WebSocket 连接管理器
manager_system = ConnectionManager()
manager_container = ConnectionManager()

# 添加系统信息和容器信息的 WebSocket 端点
@app.websocket("/ws/system")
async def websocket_system(websocket: WebSocket):
    await manager_system.connect(websocket)
    try:
        # 立即发送一次系统信息
        system_info = get_system_info()
        await websocket.send_text(json.dumps(system_info))
        
        # 保持连接并等待客户端消息
        while True:
            await websocket.receive_text()
    except Exception as e:
        print(f"[system] WebSocket error: {e}")
    finally:
        manager_system.disconnect(websocket)

@app.websocket("/ws/container")
async def websocket_container(websocket: WebSocket):
    await manager_container.connect(websocket)
    try:
        # 立即发送一次容器信息
        container_info = get_container_info()
        await websocket.send_text(json.dumps(container_info))
        
        # 保持连接并等待客户端消息
        while True:
            await websocket.receive_text()
    except Exception as e:
        print(f"[container] WebSocket error: {e}")
    finally:
        manager_container.disconnect(websocket)

# 添加定期广播系统信息和容器信息的任务
async def broadcast_system_info():
    """定期广播系统信息"""
    while True:
        if manager_system.active_connections:
            system_info = get_system_info()
            await manager_system.broadcast(json.dumps(system_info))
        await asyncio.sleep(5)  # 每5秒更新一次

async def broadcast_container_info():
    """定期广播容器信息"""
    while True:
        if manager_container.active_connections:
            container_info = get_container_info()
            await manager_container.broadcast(json.dumps(container_info))
        await asyncio.sleep(10)  # 每10秒更新一次

# API：获取带子1的检测结果并触发任务
@app.get("/belt1")
async def belt1():
    result = detection_results["belt1"]
    print(f"[belt1] result: {result}")
    if result["detect"]:
        task_queue.put({
            "belt": "belt1",
            "class": result["class"]
        })
    return detection_results["belt1"]

# API：获取带子2的检测结果并触发任务
@app.get("/belt2")
async def belt2():
    result = detection_results["belt2"]
    print(f"[belt2] result: {result}")
    if result["detect"]:
        task_queue.put({
            "belt": "belt2",
            "class": result["class"]
        })
    return detection_results["belt2"]

# FastAPI 启动事件
@app.on_event("startup")
async def startup_event():
    # 初始化 Kubernetes 客户端
    init_kubernetes()
    
    # 初始化 Redis 连接
    if init_redis():
        # 启动 Redis 监听线程
        loop = asyncio.get_event_loop()
        redis_thread = threading.Thread(target=redis_listener, args=(loop,), daemon=True)
        redis_thread.start()
        print("[Redis] 监听线程已启动")
    
    # 启动 SSH 任务监听器
    ssh_thread = threading.Thread(target=ssh_task_listener)
    ssh_thread.daemon = True
    ssh_thread.start()
    print("[SSH] 任务监听线程已启动")
    
    # 启动系统信息和容器信息广播任务
    asyncio.create_task(broadcast_system_info())
    asyncio.create_task(broadcast_container_info())
    print("[WebSocket] 系统信息和容器信息广播任务已启动")

# 如果直接运行此文件，则启动服务器
if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000, reload=True)
