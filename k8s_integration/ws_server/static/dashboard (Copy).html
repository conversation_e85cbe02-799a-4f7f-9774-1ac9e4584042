<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Brics腾飞！</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f8f9fa;
            padding-top: 20px;
        }
        .header {
            margin-bottom: 30px;
        }
        .card {
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
            border: none;
        }
        .card-header {
            background-color: #f1f8ff;
            border-bottom: 1px solid #e3f2fd;
            font-weight: 600;
            border-radius: 8px 8px 0 0 !important;
        }
        .detection-card {
            height: 100%;
        }
        .image-container {
            position: relative;
            margin-bottom: 15px;
            border-radius: 4px;
            overflow: hidden;
            background-color: #000;
            text-align: center;
        }
        .image-container img {
            max-width: 100%;
            max-height: 200px;
            display: block;
            margin: 0 auto;
        }
        .no-defect {
            background-color: #d4edda;
            color: #155724;
            padding: 8px 12px;
            border-radius: 4px;
            text-align: center;
            font-weight: 600;
        }
        .defect-alert {
            background-color: #f8d7da;
            color: #721c24;
            padding: 8px 12px;
            border-radius: 4px;
            text-align: center;
            font-weight: 600;
        }
        .defect-item {
            display: flex;
            align-items: center;
            margin-bottom: 5px;
            padding: 5px;
            background-color: #f8f9fa;
            border-radius: 4px;
        }
        .defect-number {
            font-weight: bold;
            margin-right: 10px;
            color: #495057;
        }
        .defect-class {
            font-weight: bold;
            color: #dc3545;
            margin-right: 10px;
        }
        .defect-confidence {
            color: #6c757d;
            font-size: 0.9em;
        }
        .chart-container {
            position: relative;
            height: 200px;
            margin-top: 15px;
        }
        .system-info {
            display: flex;
            flex-wrap: wrap;
        }
        .system-info-item {
            flex: 1 0 50%;
            margin-bottom: 10px;
        }
        .container-status {
            display: flex;
            flex-direction: column;
        }
        .container-item-full {
            width: 100%;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
        }
        .container-name {
            width: 120px;
            font-weight: 500;
        }
        .container-node {
            margin-left: 10px;
            color: #6c757d;
            font-size: 0.9em;
        }
        .performance-value {
            font-size: 2rem;
            font-weight: bold;
            color: #0d6efd;
            text-align: center;
            margin: 10px 0;
        }
        .performance-label {
            text-align: center;
            color: #6c757d;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>支持攻击自恢复的工业智能化弹性调度系统</h1>
            <p class="text-muted">实时监控生产线和容器状态</p>
        </div>
        
        <!-- 第一行：检测结果和主机信息 -->
        <div class="row mb-4">
            <!-- 产线1 -->
            <div class="col-md-4">
                <div class="card detection-card">
                    <div class="card-header">
                        <h5>产线1</h5>
                    </div>
                    <div class="card-body">
                        <div class="image-container">
                            <img id="belt1-image" src="/static/placeholder.jpg" alt="产线1图像">
                        </div>
                        <div id="belt1-status" class="no-defect">无缺陷</div>
                        <div id="belt1-defects" class="mt-2">
                            <!-- 这里将显示所有检测到的缺陷 -->
                        </div>
                        <div id="belt1-details" class="mt-2">
                            <p>相机ID: <span id="belt1-camera-id">-</span></p>
                            <p>检测时间: <span id="belt1-timestamp">-</span></p>
                            <p>推理模型: <span id="belt1-model">-</span></p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 产线2 -->
            <div class="col-md-4">
                <div class="card detection-card">
                    <div class="card-header">
                        <h5>产线2</h5>
                    </div>
                    <div class="card-body">
                        <div class="image-container">
                            <img id="belt2-image" src="/static/placeholder.jpg" alt="产线2图像">
                        </div>
                        <div id="belt2-status" class="no-defect">无缺陷</div>
                        <div id="belt2-defects" class="mt-2">
                            <!-- 这里将显示所有检测到的缺陷 -->
                        </div>
                        <div id="belt2-details" class="mt-2">
                            <p>相机ID: <span id="belt2-camera-id">-</span></p>
                            <p>检测时间: <span id="belt2-timestamp">-</span></p>
                            <p>推理模型: <span id="belt2-model">-</span></p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 系统信息 -->
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h5>系统信息</h5>
                    </div>
                    <div class="card-body">
                        <div class="system-info">
                            <div class="system-info-item">
                                <strong>主机名:</strong> <span id="hostname">-</span>
                            </div>
                            <div class="system-info-item">
                                <strong>操作系统:</strong> <span id="os-info">-</span>
                            </div>
                            <div class="system-info-item">
                                <strong>运行时间:</strong> <span id="uptime">-</span>
                            </div>
                            <div class="system-info-item">
                                <strong>CPU 使用率:</strong> <span id="cpu-usage">0</span>%
                            </div>
                            <div class="system-info-item">
                                <strong>内存使用率:</strong> <span id="memory-usage">0</span>%
                            </div>
                            <div class="system-info-item">
                                <strong>磁盘使用率:</strong> <span id="disk-usage">0</span>%
                            </div>
                        </div>
                        
                        <div class="chart-container">
                            <canvas id="cpuChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 第二行：性能指标和容器状态 -->
        <div class="row mb-4">
            <!-- 性能指标 -->
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h5>产线1性能指标</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-6">
                                <div class="performance-value" id="belt1-fps">0.0</div>
                                <div class="performance-label">FPS</div>
                            </div>
                            <div class="col-6">
                                <div class="performance-value" id="belt1-processing-time">0.0</div>
                                <div class="performance-label">推理时间 (ms)</div>
                            </div>
                        </div>
                        <div class="chart-container">
                            <canvas id="fpsChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 产线2性能指标 -->
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h5>产线2性能指标</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-6">
                                <div class="performance-value" id="belt2-fps">0.0</div>
                                <div class="performance-label">FPS</div>
                            </div>
                            <div class="col-6">
                                <div class="performance-value" id="belt2-processing-time">0.0</div>
                                <div class="performance-label">推理时间 (ms)</div>
                            </div>
                        </div>
                        <div class="chart-container">
                            <canvas id="belt2Chart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 容器状态 -->
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h5>容器状态</h5>
                    </div>
                    <div class="card-body">
                        <div class="container-status">
                            <div class="container-item-full">
                                <span class="container-name">Web UI:</span>
                                <span id="web-ui-status" class="badge bg-secondary">未知</span>
                                <span id="web-ui-node" class="container-node">-</span>
                            </div>
                            <div class="container-item-full">
                                <span class="container-name">处理器 1:</span>
                                <span id="processor-status" class="badge bg-secondary">未知</span>
                                <span id="processor-node" class="container-node">-</span>
                            </div>
                            <div class="container-item-full">
                                <span class="container-name">处理器 2:</span>
                                <span id="processor2-status" class="badge bg-secondary">未知</span>
                                <span id="processor2-node" class="container-node">-</span>
                            </div>
                            <div class="container-item-full">
                                <span class="container-name">Redis:</span>
                                <span id="redis-status" class="badge bg-secondary">未知</span>
                                <span id="redis-node" class="container-node">-</span>
                            </div>
                            <div class="container-item-full">
                                <span class="container-name">相机客户端 1:</span>
                                <span id="camera-client-status" class="badge bg-secondary">未知</span>
                                <span id="camera-client-node" class="container-node">-</span>
                            </div>
                            <div class="container-item-full">
                                <span class="container-name">相机客户端 2:</span>
                                <span id="camera-client2-status" class="badge bg-secondary">未知</span>
                                <span id="camera-client2-node" class="container-node">-</span>
                            </div>
                            <div class="container-item-full">
                                <span class="container-name">OpenPLC 1:</span>
                                <span id="openplc-status" class="badge bg-secondary">未知</span>
                                <span id="openplc-node" class="container-node">-</span>
                            </div>
                            <div class="container-item-full">
                                <span class="container-name">OpenPLC 2:</span>
                                <span id="openplc2-status" class="badge bg-secondary">未知</span>
                                <span id="openplc2-node" class="container-node">-</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // 初始化图表
        let cpuChart, fpsChart, belt2Chart;
        
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化 CPU 使用率图表
            const cpuChartCtx = document.getElementById('cpuChart').getContext('2d');
            cpuChart = new Chart(cpuChartCtx, {
                type: 'line',
                data: {
                    labels: Array(10).fill(''),
                    datasets: [{
                        label: 'CPU 使用率 (%)',
                        data: Array(10).fill(null),
                        borderColor: 'rgba(255, 99, 132, 1)',
                        backgroundColor: 'rgba(255, 99, 132, 0.1)',
                        tension: 0.4,
                        borderWidth: 2,
                        pointRadius: 3,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100,
                            grid: {
                                color: 'rgba(0, 0, 0, 0.05)'
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            display: false
                        }
                    }
                }
            });
            
            // 初始化产线1推理时间图表
            const fpsChartCtx = document.getElementById('fpsChart').getContext('2d');
            fpsChart = new Chart(fpsChartCtx, {
                type: 'line',
                data: {
                    labels: Array(10).fill(''),
                    datasets: [{
                        label: '推理时间 (ms)',
                        data: Array(10).fill(null),
                        borderColor: 'rgba(54, 162, 235, 1)',
                        backgroundColor: 'rgba(54, 162, 235, 0.1)',
                        tension: 0.4,
                        borderWidth: 2,
                        pointRadius: 3,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: {
                                color: 'rgba(0, 0, 0, 0.05)'
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            display: false
                        }
                    }
                }
            });
            
            // 产线2推理时间图表
            const belt2ChartCtx = document.getElementById('belt2Chart').getContext('2d');
            belt2Chart = new Chart(belt2ChartCtx, {
                type: 'line',
                data: {
                    labels: Array(10).fill(''),
                    datasets: [{
                        label: '推理时间 (ms)',
                        data: Array(10).fill(null),
                        borderColor: 'rgba(0, 122, 255, 1)',
                        backgroundColor: 'rgba(0, 122, 255, 0.1)',
                        tension: 0.4,
                        borderWidth: 2,
                        pointRadius: 3,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: {
                                color: 'rgba(0, 0, 0, 0.05)'
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            display: false
                        }
                    }
                }
            });
            
            console.log('图表初始化完成');
            
            // 定期刷新数据
            setInterval(fetchDetections, 1000);
            setInterval(fetchRedisStats, 2000);
            setInterval(fetchSystemInfo, 5000);
            setInterval(fetchContainerInfo, 10000);
            
            // 页面加载时立即获取数据
            fetchDetections();
            fetchRedisStats();
            fetchSystemInfo();
            fetchContainerInfo();
        });

        // 更新图表数据
        function updateChart(chart, label, value) {
            if (!chart || !chart.data) {
                console.error('图表未初始化:', chart);
                return;
            }
            
            console.log(`更新图表数据: ${label}, ${value}`);
            
            const maxDataPoints = 20;
            
            if (chart.data.labels.length >= maxDataPoints) {
                chart.data.labels.shift();
                chart.data.datasets[0].data.shift();
            }
            
            chart.data.labels.push(label);
            chart.data.datasets[0].data.push(value);
            chart.update('none'); // 使用 'none' 模式更新，减少动画开销
        }
        
        // 格式化时间戳
        function formatTimestamp(timestamp) {
            if (!timestamp) return '-';
            const date = new Date(timestamp);
            return date.toLocaleTimeString();
        }
        
        // 格式化运行时间
        function formatUptime(seconds) {
            if (!seconds || seconds <= 0) return '-';
            
            const days = Math.floor(seconds / 86400);
            const hours = Math.floor((seconds % 86400) / 3600);
            const minutes = Math.floor((seconds % 3600) / 60);
            
            let result = '';
            if (days > 0) result += `${days}天 `;
            if (hours > 0 || days > 0) result += `${hours}小时 `;
            result += `${minutes}分钟`;
            
            return result;
        }
        
        // 更新容器状态显示
        function updateContainerStatus(elementId, status, nodeName, uptime) {
            const element = document.getElementById(elementId);
            const nodeElement = document.getElementById(elementId.replace('-status', '-node'));
            
            if (!element) return;
            
            let statusText = '未知';
            let statusClass = 'badge bg-secondary';
            
            if (status === 'running') {
                statusText = '运行中';
                statusClass = 'badge bg-success';
            } else if (status === 'stopped') {
                statusText = '已停止';
                statusClass = 'badge bg-danger';
            }
            
            element.className = statusClass;
            element.textContent = statusText;
            
            // 更新节点信息和运行时间
            if (nodeElement) {
                let nodeText = '-';
                if (nodeName && nodeName !== 'unknown') {
                    nodeText = nodeName;
                    if (uptime && status === 'running') {
                        nodeText += ` (${uptime})`;
                    }
                }
                nodeElement.textContent = nodeText;
            }
        }
        
        // 定期获取检测结果
        function fetchDetections() {
            fetch('/api/detection_results')
                .then(response => response.json())
                .then(data => {
                    // 更新产线1数据
                    const belt1 = data.belt1;
                    if (belt1) {
                        const detections = belt1.detections || [];
                        
                        if (detections.length > 0) {
                            document.getElementById('belt1-status').className = 'defect-alert';
                            document.getElementById('belt1-status').textContent = `检测到 ${detections.length} 个缺陷`;
                            
                            // 显示所有缺陷
                            const defectsHtml = detections.map((det, index) => {
                                return `<div class="defect-item">
                                            <span class="defect-number">${index + 1}.</span>
                                            <span class="defect-class">${det.class || 'unknown'}</span>
                                            <span class="defect-confidence">(${(det.confidence * 100).toFixed(1)}%)</span>
                                        </div>`;
                            }).join('');
                            
                            document.getElementById('belt1-defects').innerHTML = defectsHtml;
                        } else {
                            document.getElementById('belt1-status').className = 'no-defect';
                            document.getElementById('belt1-status').textContent = '无缺陷';
                            document.getElementById('belt1-defects').innerHTML = '';
                        }
                        
                        document.getElementById('belt1-camera-id').textContent = belt1.camera_id || '-';
                        document.getElementById('belt1-timestamp').textContent = formatTimestamp(belt1.timestamp);
                        document.getElementById('belt1-model').textContent = belt1.model_name || '-';
                        
                        // 如果有图像数据，更新图像
                        if (belt1.image) {
                            document.getElementById('belt1-image').src = `data:image/jpeg;base64,${belt1.image}`;
                        }
                        
                        // 更新产线1推理图表
                        if (belt1.timestamp && belt1.perf_info) {
                            const timeLabel = formatTimestamp(belt1.timestamp);
                            const inferenceTime = belt1.perf_info.total_time * 1000 || 0;
                            const fps = belt1.perf_info.fps || 0;
                            updateChart(fpsChart, timeLabel, inferenceTime);
                            document.getElementById('belt1-fps').textContent = fps.toFixed(1);
                            document.getElementById('belt1-processing-time').textContent = inferenceTime.toFixed(1);
                        }
                    }
                    
                    // 更新产线2数据
                    const belt2 = data.belt2;
                    if (belt2) {
                        const detections = belt2.detections || [];
                        
                        if (detections.length > 0) {
                            document.getElementById('belt2-status').className = 'defect-alert';
                            document.getElementById('belt2-status').textContent = `检测到 ${detections.length} 个缺陷`;
                            
                            // 显示所有缺陷
                            const defectsHtml = detections.map((det, index) => {
                                return `<div class="defect-item">
                                            <span class="defect-number">${index + 1}.</span>
                                            <span class="defect-class">${det.class || 'unknown'}</span>
                                            <span class="defect-confidence">(${(det.confidence * 100).toFixed(1)}%)</span>
                                        </div>`;
                            }).join('');
                            
                            document.getElementById('belt2-defects').innerHTML = defectsHtml;
                        } else {
                            document.getElementById('belt2-status').className = 'no-defect';
                            document.getElementById('belt2-status').textContent = '无缺陷';
                            document.getElementById('belt2-defects').innerHTML = '';
                        }
                        
                        document.getElementById('belt2-camera-id').textContent = belt2.camera_id || '-';
                        document.getElementById('belt2-timestamp').textContent = formatTimestamp(belt2.timestamp);
                        document.getElementById('belt2-model').textContent = belt2.model_name || '-';
                        
                        // 如果有图像数据，更新图像
                        if (belt2.image) {
                            document.getElementById('belt2-image').src = `data:image/jpeg;base64,${belt2.image}`;
                        }
                        
                        // 更新产线2推理图表
                        if (belt2.timestamp && belt2.perf_info) {
                            const timeLabel = formatTimestamp(belt2.timestamp);
                            const inferenceTime = belt2.perf_info.total_time * 1000 || 0;
                            const fps = belt2.perf_info.fps || 0;
                            updateChart(belt2Chart, timeLabel, inferenceTime);
                            document.getElementById('belt2-fps').textContent = fps.toFixed(1);
                            document.getElementById('belt2-processing-time').textContent = inferenceTime.toFixed(1);
                        }
                    }
                })
                .catch(error => console.error('获取检测结果出错:', error));
        }
        
        // 定期获取 Redis 统计信息
        function fetchRedisStats() {
            fetch('/api/redis_stats')
                .then(response => response.json())
                .then(data => {
                    const stats = data.stats;
                    
                    // 更新通道1信息
                    if (stats.video_channel_1) {
                        document.getElementById('channel1-message-count').textContent = stats.video_channel_1.message_count;
                        document.getElementById('channel1-detection-count').textContent = stats.video_channel_1.detection_count;
                    }
                    
                    // 更新通道2信息
                    if (stats.video_channel_2) {
                        document.getElementById('channel2-message-count').textContent = stats.video_channel_2.message_count;
                        document.getElementById('channel2-detection-count').textContent = stats.video_channel_2.detection_count;
                    }
                    
                    // 更新 Redis 状态提示
                    const redisAlert = document.getElementById('redis-status-alert');
                    const ch1Age = stats.video_channel_1.last_message_age;
                    const ch2Age = stats.video_channel_2.last_message_age;
                    
                    if (!ch1Age && !ch2Age) {
                        redisAlert.className = 'alert alert-warning';
                        redisAlert.textContent = 'Redis 连接正常，但尚未收到任何消息';
                    } else if ((ch1Age && ch1Age > 60) || (ch2Age && ch2Age > 60)) {
                        redisAlert.className = 'alert alert-warning';
                        redisAlert.textContent = 'Redis 连接正常，但最近一分钟内未收到新消息';
                    } else {
                        redisAlert.className = 'alert alert-success';
                        redisAlert.textContent = 'Redis 连接正常，消息流正常';
                    }
                })
                .catch(error => console.error('获取 Redis 统计信息失败:', error));
        }
        
        // 定期获取系统信息
        function fetchSystemInfo() {
            fetch('/api/system_info')
                .then(response => response.json())
                .then(data => {
                    document.getElementById('hostname').textContent = data.hostname;
                    document.getElementById('os-info').textContent = data.os_info;
                    document.getElementById('uptime').textContent = data.uptime;
                    document.getElementById('cpu-usage').textContent = data.cpu_percent.toFixed(1);
                    document.getElementById('memory-usage').textContent = data.memory_percent.toFixed(1);
                    document.getElementById('disk-usage').textContent = data.disk_percent ? data.disk_percent.toFixed(1) : '0.0';
                    
                    // 更新CPU图表
                    const timeLabel = formatTimestamp(data.timestamp);
                    updateChart(cpuChart, timeLabel, data.cpu_percent);
                })
                .catch(error => console.error('获取系统信息失败:', error));
        }
        
        // 定期获取容器信息
        function fetchContainerInfo() {
            fetch('/api/container_info')
                .then(response => response.json())
                .then(data => {
                    console.log("容器信息:", data); // 添加调试日志
                    if (data.containers) {
                        // 使用正确的容器名称
                        updateContainerStatus('web-ui-status', data.containers.web_ui, data.container_nodes?.web_ui, data.container_uptimes?.web_ui);
                        updateContainerStatus('processor-status', data.containers.processor, data.container_nodes?.processor, data.container_uptimes?.processor);
                        updateContainerStatus('processor2-status', data.containers.processor2, data.container_nodes?.processor2, data.container_uptimes?.processor2);
                        updateContainerStatus('redis-status', data.containers.redis, data.container_nodes?.redis, data.container_uptimes?.redis);
                        updateContainerStatus('camera-client-status', data.containers.camera_client, data.container_nodes?.camera_client, data.container_uptimes?.camera_client);
                        updateContainerStatus('camera-client2-status', data.containers.camera_client2, data.container_nodes?.camera_client2, data.container_uptimes?.camera_client2);
                        updateContainerStatus('openplc-status', data.containers.openplc, data.container_nodes?.openplc, data.container_uptimes?.openplc);
                        updateContainerStatus('openplc2-status', data.containers.openplc2, data.container_nodes?.openplc2, data.container_uptimes?.openplc2);
                    }
                })
                .catch(error => console.error('获取容器信息失败:', error));
        }
    </script>
</body>
</html>
