<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Brics腾飞！</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        :root {
            --industrial-dark: #1e2126;
            --industrial-medium: #2d3339;
            --industrial-light: #3d4550;
            --industrial-accent: #ffb400;
            --industrial-success: #2ecc71;
            --industrial-danger: #e74c3c;
            --industrial-warning: #f39c12;
            --industrial-info: #3498db;
            --industrial-text: #ecf0f1;
            --industrial-text-muted: #95a5a6;
            --industrial-border: #4a5568;
        }
        
        body {
            font-family: 'Microsoft YaHei', 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: var(--industrial-dark);
            color: var(--industrial-text);
            padding-top: 20px;
        }
        
        .header {
            margin-bottom: 30px;
            border-bottom: 2px solid var(--industrial-accent);
            padding-bottom: 15px;
        }
        
        .header h1 {
            font-weight: 700;
            color: var(--industrial-text);
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }
        .card {
            border-radius: 4px;
            background-color: var(--industrial-medium);
            border: 1px solid var(--industrial-border);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
            margin-bottom: 20px;
        }
        .card-header {
            background-color: var(--industrial-light);
            border-bottom: 1px solid var(--industrial-border);
            font-weight: 600;
            color: var(--industrial-text);
            padding: 12px 15px;
            border-radius: 4px 4px 0 0 !important;
        }
        .card-body {
            padding: 15px;
            background-color: var(--industrial-medium);
            color: var(--industrial-text);
        }
        .detection-card {
            height: 75%; /* 减少高度为原来的75% */
        }
        .image-container {
            position: relative;
            margin-bottom: 15px;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3);
            width: 100%;
            background-color: var(--industrial-dark);
            height: 165px; /* 减少高度 */
        }
        .image-container img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            display: block;
        }
        .no-defect {
            background-color: #d4edda;
            color: #155724;
            padding: 8px 12px;
            border-radius: 4px;
            text-align: center;
            font-weight: 600;
        }
        .defect-alert {
            background-color: #f8d7da;
            color: #721c24;
            padding: 8px 12px;
            border-radius: 4px;
            text-align: center;
            font-weight: 600;
        }
        .defect-item {
            display: flex;
            align-items: center;
            margin-bottom: 5px;
            padding: 5px;
            background-color: #f8f9fa;
            border-radius: 4px;
        }
        .defect-number {
            font-weight: bold;
            margin-right: 10px;
            color: #495057;
        }
        .defect-class {
            font-weight: bold;
            color: #dc3545;
            margin-right: 10px;
        }
        .defect-confidence {
            color: #6c757d;
            font-size: 0.9em;
        }
        .chart-container {
            height: 100px; /* 减少图表高度 */
            margin-top: 10px;
        }
        .system-info {
            display: flex;
            flex-wrap: wrap;
        }
        .system-info-item {
            flex: 1 0 50%;
            margin-bottom: 10px;
        }
        .container-status {
            display: flex;
            flex-direction: column;
            max-height: 600px; /* 增加最大高度 */
            overflow-y: auto;
        }
        .container-item-full {
            width: 100%;
            margin-bottom: 8px;
            display: flex;
            flex-direction: column;
            padding: 10px;
            border-radius: 6px;
            background-color: rgba(0, 0, 0, 0.2);
            transition: all 0.2s ease;
        }
        .container-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 5px;
        }
        .container-details {
            display: flex;
            flex-wrap: wrap;
            font-size: 0.85rem;
            color: var(--industrial-text-muted);
        }
        .container-name {
            font-weight: 600;
            font-size: 1.1rem;
            color: var(--industrial-text);
        }
        .container-label {
            color: var(--industrial-text-muted);
            margin-right: 5px;
        }
        .container-node {
            color: var(--industrial-info);
            font-family: 'Consolas', monospace;
            margin-right: 15px;
        }
        .container-uptime {
            color: var(--industrial-text);
        }
        .badge {
            font-size: 0.9rem;
            padding: 5px 10px;
        }
        .ml-2 {
            margin-left: 0.5rem;
        }
        .performance-value {
            font-size: 1.5rem;
            font-weight: bold;
            color: var(--industrial-info);
            text-align: center;
            margin: 3px 0;
        }
        .performance-label {
            text-align: center;
            color: var(--industrial-text-muted);
            font-size: 0.85rem;
        }
        .badge.bg-success {
            background-color: var(--industrial-success) !important;
        }
        
        .badge.bg-danger {
            background-color: var(--industrial-danger) !important;
        }
        
        /* 添加工业风格的指示灯效果 */
        .indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
            position: relative;
        }
        
        .indicator.on {
            background-color: var(--industrial-success);
            box-shadow: 0 0 10px var(--industrial-success);
            animation: pulse 2s infinite;
        }
        
        .indicator.off {
            background-color: var(--industrial-danger);
            box-shadow: 0 0 5px rgba(231, 76, 60, 0.5);
        }
        
        @keyframes pulse {
            0% {
                box-shadow: 0 0 0 0 rgba(46, 204, 113, 0.7);
            }
            70% {
                box-shadow: 0 0 0 5px rgba(46, 204, 113, 0);
            }
            100% {
                box-shadow: 0 0 0 0 rgba(46, 204, 113, 0);
            }
        }
        /* 确保卡片间隙一致 */
        .row {
            margin-left: -15px;
            margin-right: -15px;
        }
        
        .col-md-4 {
            padding-left: 15px;
            padding-right: 15px;
        }
        
        .card {
            height: 100%;
            margin-bottom: 0;
        }
        
        .mb-4 {
            margin-bottom: 30px !important;
        }
        
        /* 确保卡片内容高度一致 */
        .detection-card .card-body {
            display: flex;
            flex-direction: column;
        }
        
        .detection-card .image-container {
            flex: 0 0 auto;
            height: 240px; /* 固定高度 */
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .detection-card #belt1-defects,
        .detection-card #belt2-defects {
            flex: 1 0 auto;
            min-height: 60px;
        }
        
        /* 确保容器状态卡片与其他卡片高度一致 */
        .container-status {
            max-height: 490px;
            overflow-y: auto;
        }
        
        /* 美化滚动条 */
        .container-status::-webkit-scrollbar {
            width: 8px;
        }
        
        .container-status::-webkit-scrollbar-track {
            background: var(--industrial-dark);
            border-radius: 4px;
        }
        
        .container-status::-webkit-scrollbar-thumb {
            background: var(--industrial-light);
            border-radius: 4px;
        }
        
        .container-status::-webkit-scrollbar-thumb:hover {
            background: var(--industrial-accent);
        }
        /* 添加图像加载效果 */
        .image-container img {
            transition: opacity 0.3s ease;
            opacity: 0.95;
        }

        .image-container img.loaded {
            opacity: 1;
        }

        /* 添加图像悬停效果 */
        .image-container:hover img {
            transform: scale(1.02);
            transition: transform 0.3s ease;
        }

        /* 添加图像边框 */
        .image-container {
            border: 1px solid var(--industrial-border);
            padding: 2px;
        }
        /* 添加图像加载动画 */
        @keyframes imageLoading {
            0% { opacity: 0.6; }
            50% { opacity: 0.8; }
            100% { opacity: 0.6; }
        }

        .image-container::before {
            content: "加载中...";
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: var(--industrial-text-muted);
            z-index: 0;
            animation: imageLoading 1.5s infinite;
        }

        .image-container img {
            position: relative;
            z-index: 1;
        }
        /* K8s节点卡片样式优化 */
        .node-card {
            background-color: rgba(0, 0, 0, 0.2);
            border-radius: 6px;
            padding: 16px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
            transition: all 0.3s ease;
            border-left: 4px solid var(--industrial-border);
            height: 100%;
        }

        .node-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
        }

        .node-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid var(--industrial-border);
        }

        .node-name {
            font-weight: 600;
            font-size: 1.2rem;
            color: var(--industrial-text);
        }

        .node-status {
            padding: 5px 10px;
            border-radius: 4px;
            font-size: 0.95rem;
            font-weight: 500;
        }

        .node-status.ready {
            background-color: var(--industrial-success);
            color: white;
        }

        .node-status.not-ready {
            background-color: var(--industrial-danger);
            color: white;
        }

        .node-metrics {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
            margin-bottom: 10px;
        }

        .node-metric {
            padding: 10px;
            background-color: rgba(0, 0, 0, 0.1);
            border-radius: 4px;
        }

        .node-metric-label {
            font-size: 0.9rem;
            color: var(--industrial-text-muted);
            margin-bottom: 8px;
        }

        .node-metric-value {
            font-weight: 500;
            color: var(--industrial-text);
            font-size: 1.3rem;
        }

        .progress {
            height: 8px;
            margin-top: 10px;
            background-color: var(--industrial-dark);
        }

        .node-uptime {
            font-size: 0.9rem;
            color: var(--industrial-text-muted);
            margin-top: 10px;
        }

        /* 确保卡片高度一致 */
        .h-100 {
            height: 100% !important;
        }

        /* 调整图像容器高度 */
        .image-container {
            height: 220px;
        }

        /* 响应式调整 */
        @media (max-width: 992px) {
            .col-md-3, .col-md-4 {
                margin-bottom: 20px;
            }
            
            .node-card {
                margin-bottom: 15px;
            }
        }
        /* 性能指标卡片样式 */
        .performance-metrics {
            margin-top: 5px;
        }

        .performance-value {
            font-size: 1.3rem;
            font-weight: bold;
            color: var(--industrial-info);
            text-align: center;
            margin: 3px 0;
        }

        .performance-label {
            text-align: center;
            color: var(--industrial-text-muted);
            font-size: 0.75rem;
        }

        .chart-container {
            height: 120px; /* 减少图表高度 */
            margin-top: 8px;
        }

        /* 容器项和性能指标项共用样式 */
        .container-item-full {
            width: 100%;
            margin-bottom: 12px;
            display: flex;
            flex-direction: column;
            padding: 10px;
            border-radius: 6px;
            background-color: rgba(0, 0, 0, 0.2);
            transition: all 0.2s ease;
        }

        .container-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 5px;
        }

        .container-name {
            font-weight: 600;
            font-size: 1.1rem;
            color: var(--industrial-text);
        }

        /* 确保图表在小卡片中正确显示 */
        .container-item-full .chart-container {
            width: 100%;
        }

        /* 调整第一行卡片高度 */
        .detection-card {
            height: 100%; /* 恢复为原来的100%高度 */
        }

        .container-status {
            display: flex;
            flex-direction: column;
            height: 100%; /* 减少高度为原来的75% */
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="text-center">支持攻击自恢复的工业智能化弹性调度系统</h1>
            <p class="text-center">Brics腾飞团队作品</p>
        </div>
        
        <!-- 第一行：四列布局 -->
        <div class="row mb-4">
            <!-- 产线1 -->
            <div class="col-md-3">
                <div class="card detection-card">
                    <div class="card-header">
                        <h5>产线1</h5>
                    </div>
                    <div class="card-body">
                        <div class="image-container">
                            <img id="belt1-image" src="/static/placeholder.jpg" alt="产线1图像">
                        </div>
                        <div id="belt1-status" class="no-defect">无缺陷</div>
                        <div id="belt1-defects" class="mt-2">
                            <!-- 这里将显示所有检测到的缺陷 -->
                        </div>
                        <div id="belt1-details" class="mt-2">
                            <p>相机ID: <span id="belt1-camera-id">-</span></p>
                            <p>检测时间: <span id="belt1-timestamp">-</span></p>
                            <p>推理模型: <span id="belt1-model">-</span></p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 产线2 -->
            <div class="col-md-3">
                <div class="card detection-card">
                    <div class="card-header">
                        <h5>产线2</h5>
                    </div>
                    <div class="card-body">
                        <div class="image-container">
                            <img id="belt2-image" src="/static/placeholder.jpg" alt="产线2图像">
                        </div>
                        <div id="belt2-status" class="no-defect">无缺陷</div>
                        <div id="belt2-defects" class="mt-2">
                            <!-- 这里将显示所有检测到的缺陷 -->
                        </div>
                        <div id="belt2-details" class="mt-2">
                            <p>相机ID: <span id="belt2-camera-id">-</span></p>
                            <p>检测时间: <span id="belt2-timestamp">-</span></p>
                            <p>推理模型: <span id="belt2-model">-</span></p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 产线检测性能 -->
            <div class="col-md-3">
                <div class="card h-100">
                    <div class="card-header">
                        <h5>产线检测性能</h5>
                    </div>
                    <div class="card-body">
                        <!-- 产线1性能指标 -->
                        <div class="container-item-full">
                            <div class="container-info">
                                <span class="container-name">产线1性能</span>
                            </div>
                            <div class="performance-metrics">
                                <div class="row">
                                    <div class="col-6">
                                        <div class="performance-value" id="belt1-fps">0.0</div>
                                        <div class="performance-label">FPS</div>
                                    </div>
                                    <div class="col-6">
                                        <div class="performance-value" id="belt1-processing-time">0.0</div>
                                        <div class="performance-label">推理时间 (ms)</div>
                                    </div>
                                </div>
                                <div class="chart-container">
                                    <canvas id="fpsChart"></canvas>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 产线2性能指标 -->
                        <div class="container-item-full">
                            <div class="container-info">
                                <span class="container-name">产线2性能</span>
                            </div>
                            <div class="performance-metrics">
                                <div class="row">
                                    <div class="col-6">
                                        <div class="performance-value" id="belt2-fps">0.0</div>
                                        <div class="performance-label">FPS</div>
                                    </div>
                                    <div class="col-6">
                                        <div class="performance-value" id="belt2-processing-time">0.0</div>
                                        <div class="performance-label">推理时间 (ms)</div>
                                    </div>
                                </div>
                                <div class="chart-container">
                                    <canvas id="belt2Chart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 容器状态 -->
            <div class="col-md-3">
                <div class="card h-100">
                    <div class="card-header">
                        <h5>容器状态</h5>
                    </div>
                    <div class="card-body">
                        <div class="container-status">
                            <div class="container-item-full">
                                <div class="container-info">
                                    <span class="container-name">Web UI</span>
                                    <span id="web-ui-status" class="badge bg-secondary">未知</span>
                                </div>
                                <div class="container-details">
                                    <span class="container-label">节点:</span>
                                    <span id="web-ui-node" class="container-node">-</span>
                                    <span class="container-label ml-2">运行时间:</span>
                                    <span id="web-ui-uptime" class="container-uptime">-</span>
                                </div>
                            </div>
                            
                            <div class="container-item-full">
                                <div class="container-info">
                                    <span class="container-name">处理器 1</span>
                                    <span id="processor-status" class="badge bg-secondary">未知</span>
                                </div>
                                <div class="container-details">
                                    <span class="container-label">节点:</span>
                                    <span id="processor-node" class="container-node">-</span>
                                    <span class="container-label ml-2">运行时间:</span>
                                    <span id="processor-uptime" class="container-uptime">-</span>
                                </div>
                            </div>
                            
                            <div class="container-item-full">
                                <div class="container-info">
                                    <span class="container-name">处理器 2</span>
                                    <span id="processor2-status" class="badge bg-secondary">未知</span>
                                </div>
                                <div class="container-details">
                                    <span class="container-label">节点:</span>
                                    <span id="processor2-node" class="container-node">-</span>
                                    <span class="container-label ml-2">运行时间:</span>
                                    <span id="processor2-uptime" class="container-uptime">-</span>
                                </div>
                            </div>
                            
                            <div class="container-item-full">
                                <div class="container-info">
                                    <span class="container-name">Redis</span>
                                    <span id="redis-status" class="badge bg-secondary">未知</span>
                                </div>
                                <div class="container-details">
                                    <span class="container-label">节点:</span>
                                    <span id="redis-node" class="container-node">-</span>
                                    <span class="container-label ml-2">运行时间:</span>
                                    <span id="redis-uptime" class="container-uptime">-</span>
                                </div>
                            </div>
                            
                            <div class="container-item-full">
                                <div class="container-info">
                                    <span class="container-name">相机客户端 1</span>
                                    <span id="camera-client-status" class="badge bg-secondary">未知</span>
                                </div>
                                <div class="container-details">
                                    <span class="container-label">节点:</span>
                                    <span id="camera-client-node" class="container-node">-</span>
                                    <span class="container-label ml-2">运行时间:</span>
                                    <span id="camera-client-uptime" class="container-uptime">-</span>
                                </div>
                            </div>
                            
                            <div class="container-item-full">
                                <div class="container-info">
                                    <span class="container-name">相机客户端 2</span>
                                    <span id="camera-client2-status" class="badge bg-secondary">未知</span>
                                </div>
                                <div class="container-details">
                                    <span class="container-label">节点:</span>
                                    <span id="camera-client2-node" class="container-node">-</span>
                                    <span class="container-label ml-2">运行时间:</span>
                                    <span id="camera-client2-uptime" class="container-uptime">-</span>
                                </div>
                            </div>
                            
                            <div class="container-item-full">
                                <div class="container-info">
                                    <span class="container-name">OpenPLC 1</span>
                                    <span id="openplc-status" class="badge bg-secondary">未知</span>
                                </div>
                                <div class="container-details">
                                    <span class="container-label">节点:</span>
                                    <span id="openplc-node" class="container-node">-</span>
                                    <span class="container-label ml-2">运行时间:</span>
                                    <span id="openplc-uptime" class="container-uptime">-</span>
                                </div>
                            </div>
                            
                            <div class="container-item-full">
                                <div class="container-info">
                                    <span class="container-name">OpenPLC 2</span>
                                    <span id="openplc2-status" class="badge bg-secondary">未知</span>
                                </div>
                                <div class="container-details">
                                    <span class="container-label">节点:</span>
                                    <span id="openplc2-node" class="container-node">-</span>
                                    <span class="container-label ml-2">运行时间:</span>
                                    <span id="openplc2-uptime" class="container-uptime">-</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 第二行：K8s节点状态 -->
        <div class="row mb-4">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h5>系统节点状态</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <!-- k8s-node-rp5-1 -->
                            <div class="col-md-3">
                                <div class="node-card" id="node-rp5-1">
                                    <div class="node-header">
                                        <span class="node-name">k8s-node-rp5-1</span>
                                        <span class="node-status ready">就绪</span>
                                    </div>
                                    <div class="node-metrics">
                                        <div class="node-metric">
                                            <div class="node-metric-label">CPU</div>
                                            <div class="node-metric-value"><span id="node-rp5-1-cpu">0</span>%</div>
                                            <div class="progress">
                                                <div class="progress-bar progress-bar-cpu" id="node-rp5-1-cpu-bar" role="progressbar" style="width: 0%"></div>
                                            </div>
                                        </div>
                                        <div class="node-metric">
                                            <div class="node-metric-label">内存</div>
                                            <div class="node-metric-value"><span id="node-rp5-1-memory">0</span>%</div>
                                            <div class="progress">
                                                <div class="progress-bar progress-bar-memory" id="node-rp5-1-memory-bar" role="progressbar" style="width: 0%"></div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="node-uptime mt-2">
                                        <small>运行时间: <span id="node-rp5-1-uptime">-</span></small>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- k8s-node-rp5-2 -->
                            <div class="col-md-3">
                                <div class="node-card" id="node-rp5-2">
                                    <div class="node-header">
                                        <span class="node-name">k8s-node-rp5-2</span>
                                        <span class="node-status ready">就绪</span>
                                    </div>
                                    <div class="node-metrics">
                                        <div class="node-metric">
                                            <div class="node-metric-label">CPU</div>
                                            <div class="node-metric-value"><span id="node-rp5-2-cpu">0</span>%</div>
                                            <div class="progress">
                                                <div class="progress-bar progress-bar-cpu" id="node-rp5-2-cpu-bar" role="progressbar" style="width: 0%"></div>
                                            </div>
                                        </div>
                                        <div class="node-metric">
                                            <div class="node-metric-label">内存</div>
                                            <div class="node-metric-value"><span id="node-rp5-2-memory">0</span>%</div>
                                            <div class="progress">
                                                <div class="progress-bar progress-bar-memory" id="node-rp5-2-memory-bar" role="progressbar" style="width: 0%"></div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="node-uptime mt-2">
                                        <small>运行时间: <span id="node-rp5-2-uptime">-</span></small>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- k8s-node-rp4b -->
                            <div class="col-md-3">
                                <div class="node-card" id="node-rp4b">
                                    <div class="node-header">
                                        <span class="node-name">k8s-node-rp4b</span>
                                        <span class="node-status ready">就绪</span>
                                    </div>
                                    <div class="node-metrics">
                                        <div class="node-metric">
                                            <div class="node-metric-label">CPU</div>
                                            <div class="node-metric-value"><span id="node-rp4b-cpu">0</span>%</div>
                                            <div class="progress">
                                                <div class="progress-bar progress-bar-cpu" id="node-rp4b-cpu-bar" role="progressbar" style="width: 0%"></div>
                                            </div>
                                        </div>
                                        <div class="node-metric">
                                            <div class="node-metric-label">内存</div>
                                            <div class="node-metric-value"><span id="node-rp4b-memory">0</span>%</div>
                                            <div class="progress">
                                                <div class="progress-bar progress-bar-memory" id="node-rp4b-memory-bar" role="progressbar" style="width: 0%"></div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="node-uptime mt-2">
                                        <small>运行时间: <span id="node-rp4b-uptime">-</span></small>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- k8s-node-phytium -->
                            <div class="col-md-3">
                                <div class="node-card" id="node-phytium">
                                    <div class="node-header">
                                        <span class="node-name">k8s-node-phytium</span>
                                        <span class="node-status ready">就绪</span>
                                    </div>
                                    <div class="node-metrics">
                                        <div class="node-metric">
                                            <div class="node-metric-label">CPU</div>
                                            <div class="node-metric-value"><span id="node-phytium-cpu">0</span>%</div>
                                            <div class="progress">
                                                <div class="progress-bar progress-bar-cpu" id="node-phytium-cpu-bar" role="progressbar" style="width: 0%"></div>
                                            </div>
                                        </div>
                                        <div class="node-metric">
                                            <div class="node-metric-label">内存</div>
                                            <div class="node-metric-value"><span id="node-phytium-memory">0</span>%</div>
                                            <div class="progress">
                                                <div class="progress-bar progress-bar-memory" id="node-phytium-memory-bar" role="progressbar" style="width: 0%"></div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="node-uptime mt-2">
                                        <small>运行时间: <span id="node-phytium-uptime">-</span></small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // 初始化图表
        let cpuChart, fpsChart, belt2Chart;

        // 初始化WebSocket连接
        let ws1, ws2, wsSystem, wsContainer, wsNodes;

        function initWebSockets() {
            // 产线1 WebSocket
            ws1 = new WebSocket(`ws://${window.location.host}/ws/video1`);
            ws1.onopen = () => console.log("WebSocket 1 连接已建立");
            ws1.onerror = (error) => console.error("WebSocket 1 错误:", error);
            ws1.onclose = () => {
                console.log("WebSocket 1 连接已关闭，5秒后重连");
                setTimeout(initWebSockets, 5000);
            };
            ws1.onmessage = (event) => {
                const data = JSON.parse(event.data);
                // 更新产线1数据
                updateBelt1Data(data);
            };
            
            // 产线2 WebSocket
            ws2 = new WebSocket(`ws://${window.location.host}/ws/video2`);
            ws2.onopen = () => console.log("WebSocket 2 连接已建立");
            ws2.onerror = (error) => console.error("WebSocket 2 错误:", error);
            ws2.onclose = () => {
                console.log("WebSocket 2 连接已关闭");
                // 不重复重连，因为ws1的重连会同时重连ws2
            };
            ws2.onmessage = (event) => {
                const data = JSON.parse(event.data);
                // 更新产线2数据
                updateBelt2Data(data);
            };
            
            // 系统信息 WebSocket
            wsSystem = new WebSocket(`ws://${window.location.host}/ws/system`);
            wsSystem.onopen = () => console.log("系统信息 WebSocket 连接已建立");
            wsSystem.onerror = (error) => console.error("系统信息 WebSocket 错误:", error);
            wsSystem.onclose = () => {
                console.log("系统信息 WebSocket 连接已关闭");
                // 不重复重连，因为ws1的重连会同时重连所有WebSocket
            };
            wsSystem.onmessage = (event) => {
                const data = JSON.parse(event.data);
                updateSystemInfo(data);
            };
            
            // 容器信息 WebSocket
            wsContainer = new WebSocket(`ws://${window.location.host}/ws/container`);
            wsContainer.onopen = () => console.log("容器信息 WebSocket 连接已建立");
            wsContainer.onerror = (error) => console.error("容器信息 WebSocket 错误:", error);
            wsContainer.onclose = () => {
                console.log("容器信息 WebSocket 连接已关闭");
                // 不重复重连，因为ws1的重连会同时重连所有WebSocket
            };
            wsContainer.onmessage = (event) => {
                const data = JSON.parse(event.data);
                updateContainerInfo(data);
            };
            
            // K8s节点信息 WebSocket
            wsNodes = new WebSocket(`ws://${window.location.host}/ws/nodes`);
            wsNodes.onopen = () => console.log("K8s节点信息 WebSocket 连接已建立");
            wsNodes.onerror = (error) => console.error("K8s节点信息 WebSocket 错误:", error);
            wsNodes.onclose = () => {
                console.log("K8s节点信息 WebSocket 连接已关闭");
                // 不重复重连，因为ws1的重连会同时重连所有WebSocket
            };
            wsNodes.onmessage = (event) => {
                const data = JSON.parse(event.data);
                updateNodesInfo(data);
            };
        }

        document.addEventListener('DOMContentLoaded', function() {
            // 初始化图表
            initCharts();
            
            // 初始化WebSocket连接
            initWebSockets();
        });

        // 初始化图表
        function initCharts() {
            // 产线1推理时间图表
            const fpsCtx = document.getElementById('fpsChart').getContext('2d');
            fpsChart = new Chart(fpsCtx, {
                type: 'line',
                data: {
                    labels: [],
                    datasets: [{
                        label: '推理时间 (ms)',
                        data: [],
                        borderColor: '#20c997',
                        backgroundColor: 'rgba(32, 201, 151, 0.1)',
                        borderWidth: 2,
                        tension: 0.3,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 250,
                            ticks: {
                                stepSize: 50,
                                color: 'rgba(236, 240, 241, 0.7)',
                                font: {
                                    size: 10
                                }
                            },
                            grid: {
                                color: 'rgba(236, 240, 241, 0.1)'
                            }
                        },
                        x: {
                            display: false
                        }
                    },
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    elements: {
                        point: {
                            radius: 2,
                            hoverRadius: 4
                        },
                        line: {
                            borderWidth: 2
                        }
                    }
                }
            });
            
            // 产线2推理时间图表
            const belt2Ctx = document.getElementById('belt2Chart').getContext('2d');
            belt2Chart = new Chart(belt2Ctx, {
                type: 'line',
                data: {
                    labels: [],
                    datasets: [{
                        label: '推理时间 (ms)',
                        data: [],
                        borderColor: '#fd7e14',
                        backgroundColor: 'rgba(253, 126, 20, 0.1)',
                        borderWidth: 2,
                        tension: 0.3,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 250,
                            ticks: {
                                stepSize: 50,
                                color: 'rgba(236, 240, 241, 0.7)',
                                font: {
                                    size: 10
                                }
                            },
                            grid: {
                                color: 'rgba(236, 240, 241, 0.1)'
                            }
                        },
                        x: {
                            display: false
                        }
                    },
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    elements: {
                        point: {
                            radius: 2,
                            hoverRadius: 4
                        },
                        line: {
                            borderWidth: 2
                        }
                    }
                }
            });
        }

        // 更新图表数据
        function updateChart(chart, label, value) {
            if (!chart || !chart.data) {
                console.error('图表未初始化:', chart);
                return;
            }
            
            console.log(`更新图表数据: ${label}, ${value}`);
            
            const maxDataPoints = 20;
            
            if (chart.data.labels.length >= maxDataPoints) {
                chart.data.labels.shift();
                chart.data.datasets[0].data.shift();
            }
            
            chart.data.labels.push(label);
            chart.data.datasets[0].data.push(value);
            chart.update('none'); // 使用 'none' 模式更新，减少动画开销
        }
        
        // 格式化时间戳
        function formatTimestamp(timestamp) {
            if (!timestamp) return '-';
            const date = new Date(timestamp);
            return date.toLocaleTimeString();
        }
        
        // 格式化运行时间
        function formatUptime(seconds) {
            if (!seconds || seconds <= 0) return '-';
            
            const days = Math.floor(seconds / 86400);
            const hours = Math.floor((seconds % 86400) / 3600);
            const minutes = Math.floor((seconds % 3600) / 60);
            
            let result = '';
            if (days > 0) result += `${days}天 `;
            if (hours > 0 || days > 0) result += `${hours}小时 `;
            result += `${minutes}分钟`;
            
            return result;
        }
        
        // 更新容器状态显示
        function updateContainerStatus(elementId, status, nodeName, uptime) {
            const element = document.getElementById(elementId);
            const nodeElement = document.getElementById(elementId.replace('-status', '-node'));
            const uptimeElement = document.getElementById(elementId.replace('-status', '-uptime'));
            
            if (!element) return;
            
            let statusText = '未知';
            let statusClass = 'badge bg-secondary';
            
            if (status === 'running') {
                statusText = '运行中';
                statusClass = 'badge bg-success';
            } else if (status === 'stopped') {
                statusText = '已停止';
                statusClass = 'badge bg-danger';
            }
            
            element.textContent = statusText;
            element.className = statusClass;
            
            if (nodeElement && nodeName) {
                nodeElement.textContent = nodeName;
            }
            
            if (uptimeElement && uptime) {
                uptimeElement.textContent = formatUptime(uptime);
            }
        }
        
        // 优化图像加载
        function updateImage(elementId, imageData) {
            const imgElement = document.getElementById(elementId);
            if (!imgElement) return;
            
            if (imageData) {
                // 创建一个新的Image对象来预加载图像
                const newImg = new Image();
                newImg.onload = function() {
                    // 图像加载完成后更新DOM
                    imgElement.src = this.src;
                    imgElement.classList.add('loaded');
                };
                newImg.src = `data:image/jpeg;base64,${imageData}`;
            } else {
                imgElement.src = '/static/placeholder.jpg';
                imgElement.classList.remove('loaded');
            }
        }
        
        // 更新产线1数据
        function updateBelt1Data(data) {
            // 更新图像
            updateImage('belt1-image', data.image);
            
            // 更新检测结果
            const detections = data.detections || [];
            if (detections.length > 0) {
                document.getElementById('belt1-status').className = 'defect-alert';
                document.getElementById('belt1-status').textContent = `检测到 ${detections.length} 个缺陷`;
                
                // 显示所有缺陷
                const defectsHtml = detections.map((det, index) => {
                    return `<div class="defect-item">
                                <span class="defect-number">${index + 1}.</span>
                                <span class="defect-class">${det.class || 'unknown'}</span>
                                <span class="defect-confidence">(${(det.confidence * 100).toFixed(1)}%)</span>
                            </div>`;
                }).join('');
                
                document.getElementById('belt1-defects').innerHTML = defectsHtml;
            } else {
                document.getElementById('belt1-status').className = 'no-defect';
                document.getElementById('belt1-status').textContent = '无缺陷';
                document.getElementById('belt1-defects').innerHTML = '';
            }
            
            // 更新其他信息
            document.getElementById('belt1-camera-id').textContent = data.camera_id || '-';
            document.getElementById('belt1-timestamp').textContent = formatTimestamp(data.create_timestamp);
            document.getElementById('belt1-model').textContent = data.perf_info?.model_name || '-';
            
            // 更新性能指标
            if (data.perf_info) {
                const timeLabel = formatTimestamp(data.create_timestamp);
                const inferenceTime = data.perf_info.total_time * 1000 || 0;
                const fps = data.perf_info.fps || 0;
                updateChart(fpsChart, timeLabel, inferenceTime);
                document.getElementById('belt1-fps').textContent = fps.toFixed(1);
                document.getElementById('belt1-processing-time').textContent = inferenceTime.toFixed(1);
            }
        }

        // 更新产线2数据
        function updateBelt2Data(data) {
            // 更新图像
            updateImage('belt2-image', data.image);
            
            // 更新检测结果
            const detections = data.detections || [];
            if (detections.length > 0) {
                document.getElementById('belt2-status').className = 'defect-alert';
                document.getElementById('belt2-status').textContent = `检测到 ${detections.length} 个缺陷`;
                
                // 显示所有缺陷
                const defectsHtml = detections.map((det, index) => {
                    return `<div class="defect-item">
                                <span class="defect-number">${index + 1}.</span>
                                <span class="defect-class">${det.class || 'unknown'}</span>
                                <span class="defect-confidence">(${(det.confidence * 100).toFixed(1)}%)</span>
                            </div>`;
                }).join('');
                
                document.getElementById('belt2-defects').innerHTML = defectsHtml;
            } else {
                document.getElementById('belt2-status').className = 'no-defect';
                document.getElementById('belt2-status').textContent = '无缺陷';
                document.getElementById('belt2-defects').innerHTML = '';
            }
            
            // 更新其他信息
            document.getElementById('belt2-camera-id').textContent = data.camera_id || '-';
            document.getElementById('belt2-timestamp').textContent = formatTimestamp(data.create_timestamp);
            document.getElementById('belt2-model').textContent = data.perf_info?.model_name || '-';
            
            // 更新性能指标
            if (data.perf_info) {
                const timeLabel = formatTimestamp(data.create_timestamp);
                const inferenceTime = data.perf_info.total_time * 1000 || 0;
                const fps = data.perf_info.fps || 0;
                updateChart(belt2Chart, timeLabel, inferenceTime);
                document.getElementById('belt2-fps').textContent = fps.toFixed(1);
                document.getElementById('belt2-processing-time').textContent = inferenceTime.toFixed(1);
            }
        }
        
        // 更新系统信息
        function updateSystemInfo(data) {
            document.getElementById('hostname').textContent = data.hostname;
            document.getElementById('os-info').textContent = data.os_info;
            document.getElementById('uptime').textContent = data.uptime;
            document.getElementById('cpu-usage').textContent = data.cpu_percent.toFixed(1);
            document.getElementById('memory-usage').textContent = data.memory_percent.toFixed(1);
            document.getElementById('disk-usage').textContent = data.disk_percent ? data.disk_percent.toFixed(1) : '0.0';
            
            // 更新CPU图表
            const timeLabel = formatTimestamp(data.timestamp);
            updateChart(cpuChart, timeLabel, data.cpu_percent);
        }
        
        // 更新容器信息
        function updateContainerInfo(data) {
            console.log("容器信息:", data);
            if (data.containers) {
                // 使用正确的容器名称
                updateContainerStatus('web-ui-status', data.containers.web_ui, data.container_nodes?.web_ui, data.container_uptimes?.web_ui);
                updateContainerStatus('processor-status', data.containers.processor, data.container_nodes?.processor, data.container_uptimes?.processor);
                updateContainerStatus('processor2-status', data.containers.processor2, data.container_nodes?.processor2, data.container_uptimes?.processor2);
                updateContainerStatus('redis-status', data.containers.redis, data.container_nodes?.redis, data.container_uptimes?.redis);
                updateContainerStatus('camera-client-status', data.containers.camera_client, data.container_nodes?.camera_client, data.container_uptimes?.camera_client);
                updateContainerStatus('camera-client2-status', data.containers.camera_client2, data.container_nodes?.camera_client2, data.container_uptimes?.camera_client2);
                updateContainerStatus('openplc-status', data.containers.openplc, data.container_nodes?.openplc, data.container_uptimes?.openplc);
                updateContainerStatus('openplc2-status', data.containers.openplc2, data.container_nodes?.openplc2, data.container_uptimes?.openplc2);
            }
            
            // 如果有容器迁移信息，高亮显示
            if (data.migrations && data.migrations.length > 0) {
                data.migrations.forEach(migration => {
                    const containerElement = document.getElementById(`${migration.container}-status`);
                    if (containerElement) {
                        containerElement.parentElement.parentElement.classList.add('container-migrating');
                        setTimeout(() => {
                            containerElement.parentElement.parentElement.classList.remove('container-migrating');
                        }, 2000);
                    }
                });
            }
        }

        // 更新K8s节点信息
        function updateNodesInfo(data) {
            if (!data || !data.nodes) return;
            
            // 更新每个节点的信息
            const nodeIds = ['rp5-1', 'rp5-2', 'rp4b', 'phytium'];
            
            nodeIds.forEach(nodeId => {
                const nodeData = data.nodes[`k8s-node-${nodeId}`];
                if (!nodeData) return;
                
                // 更新节点状态
                const statusElement = document.querySelector(`#node-${nodeId} .node-status`);
                if (statusElement) {
                    if (nodeData.status === 'Ready') {
                        statusElement.textContent = '就绪';
                        statusElement.className = 'node-status ready';
                    } else {
                        statusElement.textContent = '未就绪';
                        statusElement.className = 'node-status not-ready';
                    }
                }
                
                // 更新CPU和内存使用率
                const cpuElement = document.getElementById(`node-${nodeId}-cpu`);
                const cpuBarElement = document.getElementById(`node-${nodeId}-cpu-bar`);
                const memoryElement = document.getElementById(`node-${nodeId}-memory`);
                const memoryBarElement = document.getElementById(`node-${nodeId}-memory-bar`);
                const uptimeElement = document.getElementById(`node-${nodeId}-uptime`);
                
                if (cpuElement && cpuBarElement) {
                    const cpuPercent = nodeData.cpu_percent || 0;
                    cpuElement.textContent = cpuPercent.toFixed(1);
                    cpuBarElement.style.width = `${cpuPercent}%`;
                }
                
                if (memoryElement && memoryBarElement) {
                    const memoryPercent = nodeData.memory_percent || 0;
                    memoryElement.textContent = memoryPercent.toFixed(1);
                    memoryBarElement.style.width = `${memoryPercent}%`;
                }
                
                if (uptimeElement) {
                    uptimeElement.textContent = nodeData.uptime || '-';
                }
            });
        }
    </script>
</body>
</html>
