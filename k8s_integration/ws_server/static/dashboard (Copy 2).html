<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Brics腾飞！</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        :root {
            --industrial-dark: #1e2126;
            --industrial-medium: #2d3339;
            --industrial-light: #3d4550;
            --industrial-accent: #ffb400;
            --industrial-success: #2ecc71;
            --industrial-danger: #e74c3c;
            --industrial-warning: #f39c12;
            --industrial-info: #3498db;
            --industrial-text: #ecf0f1;
            --industrial-text-muted: #95a5a6;
            --industrial-border: #4a5568;
        }
        
        body {
            font-family: 'Microsoft YaHei', 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: var(--industrial-dark);
            color: var(--industrial-text);
            padding-top: 20px;
        }
        
        .header {
            margin-bottom: 30px;
            border-bottom: 2px solid var(--industrial-accent);
            padding-bottom: 15px;
        }
        
        .header h1 {
            font-weight: 700;
            color: var(--industrial-text);
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }
        .card {
            border-radius: 4px;
            background-color: var(--industrial-medium);
            border: 1px solid var(--industrial-border);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
            margin-bottom: 20px;
        }
        .card-header {
            background-color: var(--industrial-light);
            border-bottom: 1px solid var(--industrial-border);
            font-weight: 600;
            color: var(--industrial-text);
            padding: 12px 15px;
            border-radius: 4px 4px 0 0 !important;
        }
        .card-body {
            padding: 15px;
            background-color: var(--industrial-medium);
            color: var(--industrial-text);
        }
        .detection-card {
            height: 100%;
        }
        .image-container {
            position: relative;
            margin-bottom: 20px;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3);
            /* 移除固定宽高比 */
            width: 100%;
            background-color: var(--industrial-dark);
        }
        .image-container img {
            width: 100%;
            height: auto; /* 让高度自动调整 */
            object-fit: cover; /* 改为contain，确保整个图像可见 */
            display: block;
            max-height: 240px; /* 设置最大高度 */
        }
        .no-defect {
            background-color: #d4edda;
            color: #155724;
            padding: 8px 12px;
            border-radius: 4px;
            text-align: center;
            font-weight: 600;
        }
        .defect-alert {
            background-color: #f8d7da;
            color: #721c24;
            padding: 8px 12px;
            border-radius: 4px;
            text-align: center;
            font-weight: 600;
        }
        .defect-item {
            display: flex;
            align-items: center;
            margin-bottom: 5px;
            padding: 5px;
            background-color: #f8f9fa;
            border-radius: 4px;
        }
        .defect-number {
            font-weight: bold;
            margin-right: 10px;
            color: #495057;
        }
        .defect-class {
            font-weight: bold;
            color: #dc3545;
            margin-right: 10px;
        }
        .defect-confidence {
            color: #6c757d;
            font-size: 0.9em;
        }
        .chart-container {
            position: relative;
            height: 200px;
            margin-top: 15px;
        }
        .system-info {
            display: flex;
            flex-wrap: wrap;
        }
        .system-info-item {
            flex: 1 0 50%;
            margin-bottom: 10px;
        }
        .container-status {
            display: flex;
            flex-direction: column;
        }
        .container-item-full {
            width: 100%;
            margin-bottom: 12px;
            display: flex;
            align-items: center;
            padding: 8px;
            border-radius: 4px;
            background-color: rgba(0, 0, 0, 0.2);
        }
        .container-name {
            width: 120px;
            font-weight: 500;
            color: var(--industrial-text-muted);
        }
        .container-node {
            margin-left: 10px;
            color: var(--industrial-info);
            font-size: 0.9em;
            font-family: 'Consolas', monospace;
        }
        .performance-value {
            font-size: 2rem;
            font-weight: bold;
            color: #0d6efd;
            text-align: center;
            margin: 10px 0;
        }
        .performance-label {
            text-align: center;
            color: #6c757d;
            font-size: 0.9rem;
        }
        .badge.bg-success {
            background-color: var(--industrial-success) !important;
        }
        
        .badge.bg-danger {
            background-color: var(--industrial-danger) !important;
        }
        
        /* 添加工业风格的指示灯效果 */
        .indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
            position: relative;
        }
        
        .indicator.on {
            background-color: var(--industrial-success);
            box-shadow: 0 0 10px var(--industrial-success);
            animation: pulse 2s infinite;
        }
        
        .indicator.off {
            background-color: var(--industrial-danger);
            box-shadow: 0 0 5px rgba(231, 76, 60, 0.5);
        }
        
        @keyframes pulse {
            0% {
                box-shadow: 0 0 0 0 rgba(46, 204, 113, 0.7);
            }
            70% {
                box-shadow: 0 0 0 5px rgba(46, 204, 113, 0);
            }
            100% {
                box-shadow: 0 0 0 0 rgba(46, 204, 113, 0);
            }
        }
        /* 确保卡片间隙一致 */
        .row {
            margin-left: -15px;
            margin-right: -15px;
        }
        
        .col-md-4 {
            padding-left: 15px;
            padding-right: 15px;
        }
        
        .card {
            height: 100%;
            margin-bottom: 0;
        }
        
        .mb-4 {
            margin-bottom: 30px !important;
        }
        
        /* 确保卡片内容高度一致 */
        .detection-card .card-body {
            display: flex;
            flex-direction: column;
        }
        
        .detection-card .image-container {
            flex: 0 0 auto;
            height: 240px; /* 固定高度 */
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .detection-card #belt1-defects,
        .detection-card #belt2-defects {
            flex: 1 0 auto;
            min-height: 60px;
        }
        
        /* 确保容器状态卡片与其他卡片高度一致 */
        .container-status {
            max-height: 400px;
            overflow-y: auto;
        }
        
        /* 美化滚动条 */
        .container-status::-webkit-scrollbar {
            width: 8px;
        }
        
        .container-status::-webkit-scrollbar-track {
            background: var(--industrial-dark);
            border-radius: 4px;
        }
        
        .container-status::-webkit-scrollbar-thumb {
            background: var(--industrial-light);
            border-radius: 4px;
        }
        
        .container-status::-webkit-scrollbar-thumb:hover {
            background: var(--industrial-accent);
        }
        /* 添加图像加载效果 */
        .image-container img {
            transition: opacity 0.3s ease;
            opacity: 0.95;
        }

        .image-container img.loaded {
            opacity: 1;
        }

        /* 添加图像悬停效果 */
        .image-container:hover img {
            transform: scale(1.02);
            transition: transform 0.3s ease;
        }

        /* 添加图像边框 */
        .image-container {
            border: 1px solid var(--industrial-border);
            padding: 2px;
        }
        /* 添加图像加载动画 */
        @keyframes imageLoading {
            0% { opacity: 0.6; }
            50% { opacity: 0.8; }
            100% { opacity: 0.6; }
        }

        .image-container::before {
            content: "加载中...";
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: var(--industrial-text-muted);
            z-index: 0;
            animation: imageLoading 1.5s infinite;
        }

        .image-container img {
            position: relative;
            z-index: 1;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>支持攻击自恢复的工业智能化弹性调度系统</h1>
            <p class="text-muted">实时监控生产线和容器状态</p>
        </div>
        
        <!-- 第一行：检测结果和容器状态 -->
        <div class="row mb-4">
            <!-- 产线1 -->
            <div class="col-md-4">
                <div class="card detection-card">
                    <div class="card-header">
                        <h5>产线1</h5>
                    </div>
                    <div class="card-body">
                        <div class="image-container">
                            <img id="belt1-image" src="/static/placeholder.jpg" alt="产线1图像">
                        </div>
                        <div id="belt1-status" class="no-defect">无缺陷</div>
                        <div id="belt1-defects" class="mt-2">
                            <!-- 这里将显示所有检测到的缺陷 -->
                        </div>
                        <div id="belt1-details" class="mt-2">
                            <p>相机ID: <span id="belt1-camera-id">-</span></p>
                            <p>检测时间: <span id="belt1-timestamp">-</span></p>
                            <p>推理模型: <span id="belt1-model">-</span></p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 产线2 -->
            <div class="col-md-4">
                <div class="card detection-card">
                    <div class="card-header">
                        <h5>产线2</h5>
                    </div>
                    <div class="card-body">
                        <div class="image-container">
                            <img id="belt2-image" src="/static/placeholder.jpg" alt="产线2图像">
                        </div>
                        <div id="belt2-status" class="no-defect">无缺陷</div>
                        <div id="belt2-defects" class="mt-2">
                            <!-- 这里将显示所有检测到的缺陷 -->
                        </div>
                        <div id="belt2-details" class="mt-2">
                            <p>相机ID: <span id="belt2-camera-id">-</span></p>
                            <p>检测时间: <span id="belt2-timestamp">-</span></p>
                            <p>推理模型: <span id="belt2-model">-</span></p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 容器状态 (移到第一排) -->
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h5>容器状态</h5>
                    </div>
                    <div class="card-body">
                        <div class="container-status">
                            <div class="container-item-full">
                                <span class="container-name">Web UI:</span>
                                <span id="web-ui-status" class="badge bg-secondary">未知</span>
                                <span id="web-ui-node" class="container-node">-</span>
                            </div>
                            <div class="container-item-full">
                                <span class="container-name">处理器 1:</span>
                                <span id="processor-status" class="badge bg-secondary">未知</span>
                                <span id="processor-node" class="container-node">-</span>
                            </div>
                            <div class="container-item-full">
                                <span class="container-name">处理器 2:</span>
                                <span id="processor2-status" class="badge bg-secondary">未知</span>
                                <span id="processor2-node" class="container-node">-</span>
                            </div>
                            <div class="container-item-full">
                                <span class="container-name">Redis:</span>
                                <span id="redis-status" class="badge bg-secondary">未知</span>
                                <span id="redis-node" class="container-node">-</span>
                            </div>
                            <div class="container-item-full">
                                <span class="container-name">相机客户端 1:</span>
                                <span id="camera-client-status" class="badge bg-secondary">未知</span>
                                <span id="camera-client-node" class="container-node">-</span>
                            </div>
                            <div class="container-item-full">
                                <span class="container-name">相机客户端 2:</span>
                                <span id="camera-client2-status" class="badge bg-secondary">未知</span>
                                <span id="camera-client2-node" class="container-node">-</span>
                            </div>
                            <div class="container-item-full">
                                <span class="container-name">OpenPLC 1:</span>
                                <span id="openplc-status" class="badge bg-secondary">未知</span>
                                <span id="openplc-node" class="container-node">-</span>
                            </div>
                            <div class="container-item-full">
                                <span class="container-name">OpenPLC 2:</span>
                                <span id="openplc2-status" class="badge bg-secondary">未知</span>
                                <span id="openplc2-node" class="container-node">-</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 第二行：性能指标和系统信息 (系统信息移到第二排) -->
        <div class="row mb-4">
            <!-- 性能指标 -->
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h5>产线1性能指标</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-6">
                                <div class="performance-value" id="belt1-fps">0.0</div>
                                <div class="performance-label">FPS</div>
                            </div>
                            <div class="col-6">
                                <div class="performance-value" id="belt1-processing-time">0.0</div>
                                <div class="performance-label">推理时间 (ms)</div>
                            </div>
                        </div>
                        <div class="chart-container">
                            <canvas id="fpsChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 产线2性能指标 -->
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h5>产线2性能指标</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-6">
                                <div class="performance-value" id="belt2-fps">0.0</div>
                                <div class="performance-label">FPS</div>
                            </div>
                            <div class="col-6">
                                <div class="performance-value" id="belt2-processing-time">0.0</div>
                                <div class="performance-label">推理时间 (ms)</div>
                            </div>
                        </div>
                        <div class="chart-container">
                            <canvas id="belt2Chart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 系统信息 (移到第二排) -->
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h5>系统信息</h5>
                    </div>
                    <div class="card-body">
                        <div class="system-info">
                            <div class="system-info-item">
                                <strong>主机名:</strong> <span id="hostname">-</span>
                            </div>
                            <div class="system-info-item">
                                <strong>操作系统:</strong> <span id="os-info">-</span>
                            </div>
                            <div class="system-info-item">
                                <strong>运行时间:</strong> <span id="uptime">-</span>
                            </div>
                            <div class="system-info-item">
                                <strong>CPU 使用率:</strong> <span id="cpu-usage">0</span>%
                            </div>
                            <div class="system-info-item">
                                <strong>内存使用率:</strong> <span id="memory-usage">0</span>%
                            </div>
                            <div class="system-info-item">
                                <strong>磁盘使用率:</strong> <span id="disk-usage">0</span>%
                            </div>
                        </div>
                        
                        <div class="chart-container">
                            <canvas id="cpuChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // 初始化图表
        let cpuChart, fpsChart, belt2Chart;

        // 初始化WebSocket连接
        let ws1, ws2, wsSystem, wsContainer;

        function initWebSockets() {
            // 产线1 WebSocket
            ws1 = new WebSocket(`ws://${window.location.host}/ws/video1`);
            ws1.onopen = () => console.log("WebSocket 1 连接已建立");
            ws1.onerror = (error) => console.error("WebSocket 1 错误:", error);
            ws1.onclose = () => {
                console.log("WebSocket 1 连接已关闭，5秒后重连");
                setTimeout(initWebSockets, 5000);
            };
            ws1.onmessage = (event) => {
                const data = JSON.parse(event.data);
                // 更新产线1数据
                updateBelt1Data(data);
            };
            
            // 产线2 WebSocket
            ws2 = new WebSocket(`ws://${window.location.host}/ws/video2`);
            ws2.onopen = () => console.log("WebSocket 2 连接已建立");
            ws2.onerror = (error) => console.error("WebSocket 2 错误:", error);
            ws2.onclose = () => {
                console.log("WebSocket 2 连接已关闭");
                // 不重复重连，因为ws1的重连会同时重连ws2
            };
            ws2.onmessage = (event) => {
                const data = JSON.parse(event.data);
                // 更新产线2数据
                updateBelt2Data(data);
            };
            
            // 系统信息 WebSocket
            wsSystem = new WebSocket(`ws://${window.location.host}/ws/system`);
            wsSystem.onopen = () => console.log("系统信息 WebSocket 连接已建立");
            wsSystem.onerror = (error) => console.error("系统信息 WebSocket 错误:", error);
            wsSystem.onclose = () => {
                console.log("系统信息 WebSocket 连接已关闭");
                // 不重复重连，因为ws1的重连会同时重连所有WebSocket
            };
            wsSystem.onmessage = (event) => {
                const data = JSON.parse(event.data);
                updateSystemInfo(data);
            };
            
            // 容器信息 WebSocket
            wsContainer = new WebSocket(`ws://${window.location.host}/ws/container`);
            wsContainer.onopen = () => console.log("容器信息 WebSocket 连接已建立");
            wsContainer.onerror = (error) => console.error("容器信息 WebSocket 错误:", error);
            wsContainer.onclose = () => {
                console.log("容器信息 WebSocket 连接已关闭");
                // 不重复重连，因为ws1的重连会同时重连所有WebSocket
            };
            wsContainer.onmessage = (event) => {
                const data = JSON.parse(event.data);
                updateContainerInfo(data);
            };
        }

        document.addEventListener('DOMContentLoaded', function() {
            // 初始化图表
            initCharts();
            
            // 初始化WebSocket连接
            initWebSockets();
        });

        // 初始化图表
        function initCharts() {
            // CPU使用率图表
            const cpuCtx = document.getElementById('cpuChart').getContext('2d');
            cpuChart = new Chart(cpuCtx, {
                type: 'line',
                data: {
                    labels: [],
                    datasets: [{
                        label: 'CPU使用率 (%)',
                        data: [],
                        borderColor: '#0d6efd',
                        backgroundColor: 'rgba(13, 110, 253, 0.1)',
                        borderWidth: 2,
                        tension: 0.3,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100
                        }
                    },
                    plugins: {
                        legend: {
                            display: false
                        }
                    }
                }
            });
            
            // 产线1推理时间图表
            const fpsCtx = document.getElementById('fpsChart').getContext('2d');
            fpsChart = new Chart(fpsCtx, {
                type: 'line',
                data: {
                    labels: [],
                    datasets: [{
                        label: '推理时间 (ms)',
                        data: [],
                        borderColor: '#20c997',
                        backgroundColor: 'rgba(32, 201, 151, 0.1)',
                        borderWidth: 2,
                        tension: 0.3,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    },
                    plugins: {
                        legend: {
                            display: false
                        }
                    }
                }
            });
            
            // 产线2推理时间图表
            const belt2Ctx = document.getElementById('belt2Chart').getContext('2d');
            belt2Chart = new Chart(belt2Ctx, {
                type: 'line',
                data: {
                    labels: [],
                    datasets: [{
                        label: '推理时间 (ms)',
                        data: [],
                        borderColor: '#fd7e14',
                        backgroundColor: 'rgba(253, 126, 20, 0.1)',
                        borderWidth: 2,
                        tension: 0.3,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    },
                    plugins: {
                        legend: {
                            display: false
                        }
                    }
                }
            });
            
            console.log('图表初始化完成');
        }

        // 更新图表数据
        function updateChart(chart, label, value) {
            if (!chart || !chart.data) {
                console.error('图表未初始化:', chart);
                return;
            }
            
            console.log(`更新图表数据: ${label}, ${value}`);
            
            const maxDataPoints = 20;
            
            if (chart.data.labels.length >= maxDataPoints) {
                chart.data.labels.shift();
                chart.data.datasets[0].data.shift();
            }
            
            chart.data.labels.push(label);
            chart.data.datasets[0].data.push(value);
            chart.update('none'); // 使用 'none' 模式更新，减少动画开销
        }
        
        // 格式化时间戳
        function formatTimestamp(timestamp) {
            if (!timestamp) return '-';
            const date = new Date(timestamp);
            return date.toLocaleTimeString();
        }
        
        // 格式化运行时间
        function formatUptime(seconds) {
            if (!seconds || seconds <= 0) return '-';
            
            const days = Math.floor(seconds / 86400);
            const hours = Math.floor((seconds % 86400) / 3600);
            const minutes = Math.floor((seconds % 3600) / 60);
            
            let result = '';
            if (days > 0) result += `${days}天 `;
            if (hours > 0 || days > 0) result += `${hours}小时 `;
            result += `${minutes}分钟`;
            
            return result;
        }
        
        // 更新容器状态显示
        function updateContainerStatus(elementId, status, nodeName, uptime) {
            const element = document.getElementById(elementId);
            const nodeElement = document.getElementById(elementId.replace('-status', '-node'));
            
            if (!element) return;
            
            let statusText = '未知';
            let statusClass = 'badge bg-secondary';
            let indicatorClass = '';
            
            if (status === 'running') {
                statusText = '运行中';
                statusClass = 'badge bg-success';
                indicatorClass = 'indicator on';
            } else if (status === 'stopped') {
                statusText = '已停止';
                statusClass = 'badge bg-danger';
                indicatorClass = 'indicator off';
            }
            
            // 添加指示灯
            element.innerHTML = `<span class="${indicatorClass}"></span>${statusText}`;
            element.className = statusClass;
            
            // 更新节点信息和运行时间
            if (nodeElement) {
                let nodeText = '-';
                if (nodeName && nodeName !== 'unknown') {
                    nodeText = nodeName;
                    if (uptime && status === 'running') {
                        nodeText += ` (${uptime})`;
                    }
                }
                nodeElement.textContent = nodeText;
            }
        }
        
        // 优化图像加载
        function updateImage(elementId, imageData) {
            const imgElement = document.getElementById(elementId);
            if (!imgElement) return;
            
            if (imageData) {
                // 创建一个新的Image对象来预加载图像
                const newImg = new Image();
                newImg.onload = function() {
                    // 图像加载完成后更新DOM
                    imgElement.src = this.src;
                    imgElement.classList.add('loaded');
                };
                newImg.src = `data:image/jpeg;base64,${imageData}`;
            } else {
                imgElement.src = '/static/placeholder.jpg';
                imgElement.classList.remove('loaded');
            }
        }
        
        // 更新产线1数据
        function updateBelt1Data(data) {
            // 更新图像
            updateImage('belt1-image', data.image);
            
            // 更新检测结果
            const detections = data.detections || [];
            if (detections.length > 0) {
                document.getElementById('belt1-status').className = 'defect-alert';
                document.getElementById('belt1-status').textContent = `检测到 ${detections.length} 个缺陷`;
                
                // 显示所有缺陷
                const defectsHtml = detections.map((det, index) => {
                    return `<div class="defect-item">
                                <span class="defect-number">${index + 1}.</span>
                                <span class="defect-class">${det.class || 'unknown'}</span>
                                <span class="defect-confidence">(${(det.confidence * 100).toFixed(1)}%)</span>
                            </div>`;
                }).join('');
                
                document.getElementById('belt1-defects').innerHTML = defectsHtml;
            } else {
                document.getElementById('belt1-status').className = 'no-defect';
                document.getElementById('belt1-status').textContent = '无缺陷';
                document.getElementById('belt1-defects').innerHTML = '';
            }
            
            // 更新其他信息
            document.getElementById('belt1-camera-id').textContent = data.camera_id || '-';
            document.getElementById('belt1-timestamp').textContent = formatTimestamp(data.create_timestamp);
            document.getElementById('belt1-model').textContent = data.perf_info?.model_name || '-';
            
            // 更新性能指标
            if (data.perf_info) {
                const timeLabel = formatTimestamp(data.create_timestamp);
                const inferenceTime = data.perf_info.total_time * 1000 || 0;
                const fps = data.perf_info.fps || 0;
                updateChart(fpsChart, timeLabel, inferenceTime);
                document.getElementById('belt1-fps').textContent = fps.toFixed(1);
                document.getElementById('belt1-processing-time').textContent = inferenceTime.toFixed(1);
            }
        }

        // 更新产线2数据
        function updateBelt2Data(data) {
            // 更新图像
            updateImage('belt2-image', data.image);
            
            // 更新检测结果
            const detections = data.detections || [];
            if (detections.length > 0) {
                document.getElementById('belt2-status').className = 'defect-alert';
                document.getElementById('belt2-status').textContent = `检测到 ${detections.length} 个缺陷`;
                
                // 显示所有缺陷
                const defectsHtml = detections.map((det, index) => {
                    return `<div class="defect-item">
                                <span class="defect-number">${index + 1}.</span>
                                <span class="defect-class">${det.class || 'unknown'}</span>
                                <span class="defect-confidence">(${(det.confidence * 100).toFixed(1)}%)</span>
                            </div>`;
                }).join('');
                
                document.getElementById('belt2-defects').innerHTML = defectsHtml;
            } else {
                document.getElementById('belt2-status').className = 'no-defect';
                document.getElementById('belt2-status').textContent = '无缺陷';
                document.getElementById('belt2-defects').innerHTML = '';
            }
            
            // 更新其他信息
            document.getElementById('belt2-camera-id').textContent = data.camera_id || '-';
            document.getElementById('belt2-timestamp').textContent = formatTimestamp(data.create_timestamp);
            document.getElementById('belt2-model').textContent = data.perf_info?.model_name || '-';
            
            // 更新性能指标
            if (data.perf_info) {
                const timeLabel = formatTimestamp(data.create_timestamp);
                const inferenceTime = data.perf_info.total_time * 1000 || 0;
                const fps = data.perf_info.fps || 0;
                updateChart(belt2Chart, timeLabel, inferenceTime);
                document.getElementById('belt2-fps').textContent = fps.toFixed(1);
                document.getElementById('belt2-processing-time').textContent = inferenceTime.toFixed(1);
            }
        }
        
        // 更新系统信息
        function updateSystemInfo(data) {
            document.getElementById('hostname').textContent = data.hostname;
            document.getElementById('os-info').textContent = data.os_info;
            document.getElementById('uptime').textContent = data.uptime;
            document.getElementById('cpu-usage').textContent = data.cpu_percent.toFixed(1);
            document.getElementById('memory-usage').textContent = data.memory_percent.toFixed(1);
            document.getElementById('disk-usage').textContent = data.disk_percent ? data.disk_percent.toFixed(1) : '0.0';
            
            // 更新CPU图表
            const timeLabel = formatTimestamp(data.timestamp);
            updateChart(cpuChart, timeLabel, data.cpu_percent);
        }
        
        // 更新容器信息
        function updateContainerInfo(data) {
            console.log("容器信息:", data);
            if (data.containers) {
                // 使用正确的容器名称
                updateContainerStatus('web-ui-status', data.containers.web_ui, data.container_nodes?.web_ui, data.container_uptimes?.web_ui);
                updateContainerStatus('processor-status', data.containers.processor, data.container_nodes?.processor, data.container_uptimes?.processor);
                updateContainerStatus('processor2-status', data.containers.processor2, data.container_nodes?.processor2, data.container_uptimes?.processor2);
                updateContainerStatus('redis-status', data.containers.redis, data.container_nodes?.redis, data.container_uptimes?.redis);
                updateContainerStatus('camera-client-status', data.containers.camera_client, data.container_nodes?.camera_client, data.container_uptimes?.camera_client);
                updateContainerStatus('camera-client2-status', data.containers.camera_client2, data.container_nodes?.camera_client2, data.container_uptimes?.camera_client2);
                updateContainerStatus('openplc-status', data.containers.openplc, data.container_nodes?.openplc, data.container_uptimes?.openplc);
                updateContainerStatus('openplc2-status', data.containers.openplc2, data.container_nodes?.openplc2, data.container_uptimes?.openplc2);
            }
        }
    </script>
</body>
</html>
