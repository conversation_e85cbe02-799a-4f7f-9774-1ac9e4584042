# main.py
import redis
import asyncio
import json
import os
import queue
import time
import threading
import paramiko
from fastapi import FastAPI, WebSocket

app = FastAPI()
redis_host1 = os.getenv("REDIS_HOST_1", "localhost")
redis_port1 = int(os.getenv("REDIS_PORT_1", 6379))
redis_host2 = os.getenv("REDIS_HOST_2", "localhost")
redis_port2 = int(os.getenv("REDIS_PORT_2", 6379))
arm_host = os.getenv("ARM_HOST", "*************")
r1 = redis.Redis(host=redis_host1, port=redis_port1, db=0)
r2 = redis.Redis(host=redis_host2, port=redis_port2, db=0)
task_queue = queue.Queue()
detection_results = {
    "belt1": {"status": "waiting", "detect": None, "timestamp": None},
    "belt2": {"status": "waiting", "detect": None, "timestamp": None}
}

class ConnectionManager:
    def __init__(self):
        self.active_connections = set()

    async def connect(self, websocket: WebSocket):
        await websocket.accept()
        self.active_connections.add(websocket)

    def disconnect(self, websocket: WebSocket):
        self.active_connections.remove(websocket)

    async def broadcast(self, message: str):
        for connection in self.active_connections:
            await connection.send_text(message)

class PersistentSSHClient:
    def __init__(self, host="*************", port=22, username="ubuntu", password="ubuntu", key_filename=None, timeout=5):
        self.host = host
        self.port = port
        self.username = username
        self.password = password
        self.key_filename = key_filename
        self.timeout = timeout
        self.client = None
        self.lock = threading.Lock()  # 多线程安全执行命令

        try:
            self.connect()
        except Exception as e:
            print(f"[SSH] 连接失败: {e}")

    def connect(self):
        """初始化 SSH 连接"""
        self.client = paramiko.SSHClient()
        self.client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        self.client.connect(
            hostname=self.host,
            port=self.port,
            username=self.username,
            password=self.password,
            key_filename=self.key_filename,
            timeout=self.timeout
        )
        print(f"[SSH] Connected to {self.host}")

    def execute_command(self, command):
        """执行 SSH 命令"""
        with self.lock:
            if not self.client:
                self.connect()
            stdin, stdout, stderr = self.client.exec_command(command)
            exit_status = stdout.channel.recv_exit_status()  # 等待命令执行完成
            output = stdout.read().decode('utf-8')
            error = stderr.read().decode('utf-8')
            if exit_status != 0:
                raise Exception(f"Command failed with exit status {exit_status}: {error}")
            return output.strip()
        
    def close(self):
        if self.client:
            self.client.close()
            print(f"[SSH] Disconnected from {self.host}")

manager_video1 = ConnectionManager()
manager_video2 = ConnectionManager()

arm_client = PersistentSSHClient(host=arm_host)

def ssh_task_listener():
    class_dict = {
        "bruise": "0",
        "laps": "1",
        "scratches": "2",
    }
    while True:
        if not task_queue.empty():
            task = task_queue.get()
            belt = task.get("belt")
            belt_n = belt.split("belt")[-1]  # 获取带子编号
            if belt == "belt1":
                belt_n = "1"
            elif belt == "belt2":
                belt_n = "2"

            class_name = task.get("class")
            class_n = class_dict.get(class_name)  # 默认使用 bruise 类别
            # 这里使用 paramiko 发送 SSH 命令
            try:
                # ssh
                print(f"[SSH] 发送命令: sudo python3 /home/<USER>/Ai_FPV/YDS/yds_{belt_n}_{class_n}.py")
                arm_client.execute_command(f"sudo python3 /home/<USER>/Ai_FPV/YDS/yds_{belt_n}_{class_n}.py")
            except Exception as e:
                print(f"[SSH] 执行失败: {e}")
        time.sleep(0.1)  # 每秒检查一次任务队列

def process_detection_message(channel: str, data: str):
    """Process detection message and update storage"""
    try:
        result = json.loads(data)
        """
        [{"class": class_name,
        "confidence": 0.99,
        "box": [x1, y1, x2, y2]}]"""
        detections = result.get("detections", [])
        if channel == "video_channel_1":
            if len(detections) > 0:
                detection_results["belt1"]["status"] = "success"
                detection_results["belt1"]["detect"] = 1
                detection_results["belt1"]["class"] = detections[0].get("class", "unknown")
            else:
                detection_results["belt1"]["status"] = "success"
                detection_results["belt1"]["detect"] = 0
        elif channel == "video_channel_2":
            if len(detections) > 0:
                detection_results["belt2"]["status"] = "success"
                detection_results["belt2"]["detect"] = 1
                detection_results["belt2"]["class"] = detections[0].get("class", "unknown")
            else:
                detection_results["belt2"]["status"] = "success"
                detection_results["belt2"]["detect"] = 0
    except json.JSONDecodeError:
        print(f"Invalid JSON received on channel {channel}: {data}")

def redis_listener_1(loop):
    pubsub = r1.pubsub()
    pubsub.subscribe("video_channel_1", "video_channel_2")  # 使用订阅的频道名称
    flag = set()
    for message in pubsub.listen():
        if message["type"] == "message":
            # 直接转发原始消息
            flag.add(message["channel"])
            channel = message["channel"].decode()
            data = message["data"].decode()
            if len(flag) < 2:
                print(f"[Redis] 接收到来自 {channel} 的消息")
            process_detection_message(channel, data)
            if channel == "video_channel_1":
                asyncio.run_coroutine_threadsafe(
                    manager_video1.broadcast(data),
                    loop
                )
            elif channel == "video_channel_2":
                asyncio.run_coroutine_threadsafe(
                    manager_video2.broadcast(data),
                    loop
                )

def redis_listener_2(loop):
    pubsub = r2.pubsub()
    pubsub.subscribe("video_channel_1", "video_channel_2")  # 使用订阅的频道名称
    flag = set()
    for message in pubsub.listen():
        if message["type"] == "message":
            # 直接转发原始消息
            flag.add(message["channel"])
            channel = message["channel"].decode()
            data = message["data"].decode()
            if len(flag) < 2:
                print(f"[Redis] 接收到来自 {channel} 的消息")
            process_detection_message(channel, data)
            if channel == "video_channel_1":
                asyncio.run_coroutine_threadsafe(
                    manager_video1.broadcast(data),
                    loop
                )
            elif channel == "video_channel_2":
                asyncio.run_coroutine_threadsafe(
                    manager_video2.broadcast(data),
                    loop
                )

@app.on_event("startup")
async def startup_event():
    loop = asyncio.get_event_loop()
    thread = threading.Thread(target=redis_listener_1, args=(loop,))
    thread.daemon = True
    thread.start()

    thread2 = threading.Thread(target=redis_listener_2, args=(loop,))
    thread2.daemon = True
    thread2.start()

    ssh_thread = threading.Thread(target=ssh_task_listener)
    ssh_thread.daemon = True
    ssh_thread.start()

@app.websocket("/ws/video1")
async def websocket_video1(websocket: WebSocket):
    await manager_video1.connect(websocket)
    try:
        while True:
            await websocket.receive_text()
    except Exception as e:
        print(f"[video1] WebSocket error: {e}")
    finally:
        manager_video1.disconnect(websocket)

@app.websocket("/ws/video2")
async def websocket_video2(websocket: WebSocket):
    await manager_video2.connect(websocket)
    try:
        while True:
            await websocket.receive_text()
    except Exception as e:
        print(f"[video2] WebSocket error: {e}")
    finally:
        manager_video2.disconnect(websocket)

@app.get("/belt1")
async def belt1():
    result = detection_results["belt1"]
    print(f"[belt1] result: {result}")
    if result["detect"]:
        task_queue.put({
            "belt": "belt1",
            "class": result["class"]
        })
    return detection_results["belt1"]

@app.get("/belt2")
async def belt1():
    result = detection_results["belt2"]
    print(f"[belt2] result: {result}")
    if result["detect"]:
        task_queue.put({
            "belt": "belt2",
            "class": result["class"]
        })
    return detection_results["belt2"]


