#!/bin/bash

echo "测试服务连接性..."

# 获取所有运行中的Pod
echo -e "\n获取所有Pod..."
kubectl get pods -n test-system -o wide

# 测试processor-service内部端口
echo -e "\n测试processor-service内部端口..."
kubectl exec -n test-system processor-service-b98cc7b85-tmzkk -- sh -c "timeout 1 bash -c '</dev/tcp/127.0.0.1/50051' && echo '成功' || echo '失败'"

# 获取image-capture Pod
IMAGE_CAPTURE_POD=$(kubectl get pods -n test-system -l app=image-capture-2 -o jsonpath='{.items[0].metadata.name}' 2>/dev/null)

if [ -n "$IMAGE_CAPTURE_POD" ]; then
  echo -e "\n测试从image-capture-2 Pod访问processor-service-2..."
  kubectl exec -n test-system $IMAGE_CAPTURE_POD -- sh -c "timeout 1 bash -c '</dev/tcp/processor-service-2/50052' && echo '成功' || echo '失败'"
  
  echo -e "\n检查image-capture-2环境变量..."
  kubectl exec -n test-system $IMAGE_CAPTURE_POD -- env | grep GRPC
  
  echo -e "\n检查image-capture-2日志..."
  kubectl logs -n test-system $IMAGE_CAPTURE_POD --tail=20
fi

# 测试从集群外部访问
echo -e "\n测试从集群外部访问..."
NODE_IP=$(kubectl get nodes -o jsonpath='{.items[0].status.addresses[?(@.type=="InternalIP")].address}')
echo "节点IP: $NODE_IP"
echo "测试processor-service NodePort (30869)..."
timeout 1 bash -c "</dev/tcp/$NODE_IP/30869" && echo "成功" || echo "失败"
echo "测试processor-service-2 NodePort (30548)..."
timeout 1 bash -c "</dev/tcp/$NODE_IP/30548" && echo "成功" || echo "失败"