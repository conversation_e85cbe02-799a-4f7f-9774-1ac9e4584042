# ConfigMap: param.yaml 配置
apiVersion: v1
kind: ConfigMap
metadata:
  name: detector-config-1
  namespace: test-system
data:
  param.yaml: |
   camera_id: camera-1
   source: 0
   small_modelpath_ncnn_param: "/models/eopt_int8.param"
   small_modelpath_ncnn_bin: "/models/eopt_int8.bin"
   large_modelpath_ncnn_param: "/models/sopt.param"
   large_modelpath_ncnn_bin: "/models/sopt.bin"
   small_model_name: "v5lite_e_int8"
   large_model_name: "v5lite_s"
   classfile: "/models/label_names"
   outfolder: "stream_results"
   confThreshold: 0.25
   nmsThreshold: 0.45
   save_interval: 0
   record: false
   no_show: true
   fps_limit: 30
   use_large_model: true
   cpu_usage_threshold: 0.7
   num_threads: 4
   use_gpu: false
   use_int8_small: false
   use_int8_large: true
   target_size: 320
---
# ConfigMap: param.yaml 配置
apiVersion: v1
kind: ConfigMap
metadata:
  name: detector-config-2
  namespace: test-system
data:
  param.yaml: |
   camera_id: camera-2
   source: 2
   small_modelpath_ncnn_param: "/models/eopt_int8.param"
   small_modelpath_ncnn_bin: "/models/eopt_int8.bin"
   large_modelpath_ncnn_param: "/models/sopt.param"
   large_modelpath_ncnn_bin: "/models/sopt.bin"
   small_model_name: "v5lite_e_int8"
   large_model_name: "v5lite_s"
   classfile: "/models/label_names"
   outfolder: "stream_results"
   confThreshold: 0.25
   nmsThreshold: 0.45
   save_interval: 0
   record: false
   no_show: true
   fps_limit: 30
   use_large_model: true
   cpu_usage_threshold: 0.7
   num_threads: 4
   use_gpu: false
   use_int8_small: false
   use_int8_large: true
   target_size: 320

---
# image-capture.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: image-capture
  namespace: test-system
spec:
  replicas: 1
  selector:
    matchLabels:
      app: image-capture
  template:
    metadata:
      labels:
        app: image-capture
    spec:
      containers:
        - name: capture
          image: docker.io/library/k8s_integration-camera-client:latest
          imagePullPolicy: IfNotPresent
          env:
            - name: GRPC_HOST
              #value: processor-service.test-system.svc.cluster.local
              #value: processor-service
              value: "**************"
            - name: GRPC_PORT
              value: "50051"
          resources:
            limits:
              cpu: "1"
          securityContext:
            privileged: true
          volumeMounts:
            - name: video
              mountPath: /dev/video0
              readOnly: true
            - name: config-volume
              mountPath: /app/param.yaml
              subPath: param.yaml
      volumes:
        - name: video
          hostPath:
            path: /dev/video0
        - name: config-volume
          configMap:
            name: detector-config-1
      # kubectl label nodes <node-name> <label-key>=<label-value>
      nodeSelector:
        sensing_device: sensing_node
---
# image-capture.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: image-capture-2
  namespace: test-system
spec:
  replicas: 1
  selector:
    matchLabels:
      app: image-capture-2
  template:
    metadata:
      labels:
        app: image-capture-2
    spec:
      containers:
        - name: capture
          image: docker.io/library/k8s_integration-camera-client:latest
          imagePullPolicy: IfNotPresent
          env:
            - name: GRPC_HOST
              value: **************
            - name: GRPC_PORT
              value: "50052"
          resources:
            limits:
              cpu: "1"
          securityContext:
            privileged: true
          volumeMounts:
            - name: video
              mountPath: /dev/video2
              readOnly: true
            - name: config-volume
              mountPath: /app/param.yaml
              subPath: param.yaml
      volumes:
        - name: video
          hostPath:
            path: /dev/video2
        - name: config-volume
          configMap:
            name: detector-config-2
      # kubectl label nodes <node-name> <label-key>=<label-value>
      nodeSelector:
        sensing_device: sensing_node