from flask import Flask, render_template, jsonify
import redis
import json
import os
import time
import threading
from datetime import datetime
import platform
import socket
import subprocess
import psutil

app = Flask(__name__)
app.config['SECRET_KEY'] = 'your-secret-key'

# 从环境变量获取 Redis 配置
redis_host = os.getenv('REDIS_HOST', 'redis')
redis_port = int(os.getenv('REDIS_PORT', 6379))
r = redis.Redis(host=redis_host, port=redis_port, decode_responses=True)

# 存储最新的检测结果
latest_detections = {
    "belt1": {"status": "waiting", "detect": 0, "timestamp": None},
    "belt2": {"status": "waiting", "detect": 0, "timestamp": None}
}

# 存储性能指标
performance_metrics = {
    "cpu_usage": 0,
    "memory_usage": 0,
    "avg_processing_time": 0,
    "fps": 0,
    "timestamp": datetime.now().isoformat()
}

def redis_listener():
    """监听 Redis 频道并更新最新数据"""
    pubsub = r.pubsub()
    # 订阅多个可能的频道
    channels_to_monitor = ["video_frames", "video_channel_1", "video_channel_2"]
    pubsub.subscribe(*channels_to_monitor)
    print(f"Redis 监听器已启动，等待消息... 监听频道: {channels_to_monitor}")
    
    for message in pubsub.listen():
        if message['type'] == 'message':
            try:
                # 由于 decode_responses=True，消息已经是字符串，不需要再解码
                channel = message['channel']  # 已经是字符串
                data = json.loads(message['data'])  # 已经是字符串，直接解析
                
                print(f"收到来自频道 {channel} 的消息")
                
                # 确定是哪个产线
                camera_id = data.get("camera_id", "")
                print(f"相机ID: {camera_id}")
                
                # 根据频道或相机ID确定产线
                belt_key = None
                if channel == "video_channel_1" or "1" in camera_id:
                    belt_key = "belt1"
                elif channel == "video_channel_2" or "2" in camera_id:
                    belt_key = "belt2"
                else:
                    # 如果无法确定，尝试从其他信息推断
                    if "belt1" in str(data) or "belt_1" in str(data):
                        belt_key = "belt1"
                    elif "belt2" in str(data) or "belt_2" in str(data):
                        belt_key = "belt2"
                    else:
                        # 默认为belt1
                        belt_key = "belt1"
                
                print(f"确定为产线: {belt_key}")
                
                # 更新检测结果
                detections = data.get("detections", [])
                perf_info = data.get("perf_info", {})
                
                # 打印完整消息以便调试
                print(f"消息内容: {json.dumps(data, indent=2)[:200]}...")
                
                # 保存所有检测结果，而不仅仅是第一个
                latest_detections[belt_key] = {
                    "status": "success",
                    "detect": len(detections) > 0,
                    "detections": detections,  # 保存所有检测结果
                    "camera_id": camera_id,
                    "timestamp": data.get("create_timestamp", datetime.now().isoformat()),
                    "model_name": perf_info.get("model_name", ""),
                    "image": data.get("image", ""),
                    "perf_info": perf_info  # 添加性能信息
                }
                
                # 更新性能指标
                if perf_info:
                    performance_metrics.update({
                        "cpu_usage": perf_info.get("cpu_usage", 0) * 100,  # 转换为百分比
                        "memory_usage": perf_info.get("memory_usage", 0),
                        "avg_processing_time": perf_info.get("total_time", 0) * 1000,  # 转换为毫秒
                        "fps": perf_info.get("fps", 0),
                        "timestamp": datetime.now().isoformat()
                    })
                print(f"更新 {belt_key} 的检测结果: 检测到 {len(detections)} 个对象")
                print(f"性能指标: CPU={performance_metrics.get('cpu_usage', 0):.1f}%, FPS={performance_metrics.get('fps', 0):.1f}")
            except Exception as e:
                print(f"处理 Redis 消息时出错: {e}")
                import traceback
                traceback.print_exc()

@app.route('/')
def index():
    """主页"""
    return render_template('index.html')

@app.route('/api/latest_detections')
def get_latest_detections():
    """获取最新的检测结果"""
    return jsonify({
        'belt1': latest_detections["belt1"],
        'belt2': latest_detections["belt2"],
        'timestamp': datetime.now().isoformat()
    })

@app.route('/api/performance')
def get_performance():
    """获取性能指标"""
    return jsonify(performance_metrics)

@app.route('/api/host_info')
def get_host_info():
    """获取主机和容器信息"""
    # 获取主机名
    hostname = socket.gethostname()
    
    # 获取操作系统信息
    os_info = f"{platform.system()} {platform.release()}"
    
    # 获取运行时间
    try:
        uptime_seconds = int(psutil.boot_time())
        current_time = int(time.time())
        uptime_seconds = current_time - uptime_seconds
        
        days, remainder = divmod(uptime_seconds, 86400)
        hours, remainder = divmod(remainder, 3600)
        minutes, seconds = divmod(remainder, 60)
        
        uptime = f"{days}天 {hours}小时 {minutes}分钟"
    except:
        uptime = "未知"
    
    # 检查容器状态
    containers = {
        "web_ui": "running",
        "processor": "running",
        "processor2": "running",
        "redis": "running",
        "camera_client": "running",
        "camera_client2": "running",
        "openplc": "running",
        "openplc2": "running"
    }
    
    # 存储容器运行时间（秒）
    container_uptimes = {
        "web_ui": 0,
        "processor": 0,
        "processor2": 0,
        "redis": 0,
        "camera_client": 0,
        "camera_client2": 0,
        "openplc": 0,
        "openplc2": 0
    }
    
    # 存储容器运行节点
    container_nodes = {
        "web_ui": "本地",
        "processor": "本地",
        "processor2": "本地",
        "redis": "本地",
        "camera_client": "本地",
        "camera_client2": "本地",
        "openplc": "本地",
        "openplc2": "本地"
    }
    
    # 如果在 Docker 环境中，可以尝试检查容器状态
    try:
        # 获取容器详细信息，包括创建时间和节点
        docker_inspect = subprocess.run(
            ["docker", "ps", "-a", "--format", "{{.Names}}|{{.Status}}|{{.CreatedAt}}"],
            capture_output=True,
            text=True,
            timeout=3
        )
        
        if docker_inspect.returncode == 0:
            container_info = docker_inspect.stdout.strip().split('\n')
            current_time = time.time()
            
            for container in container_info:
                if not container:
                    continue
                
                parts = container.split('|')
                if len(parts) < 2:
                    continue
                
                container_name = parts[0]
                status = parts[1]
                
                # 检查Web UI容器
                if "web-ui" in container_name.lower() or "web_ui" in container_name.lower():
                    containers["web_ui"] = "running" if "Up" in status else "stopped"
                    if "Up" in status:
                        container_uptimes["web_ui"] = uptime_seconds
                
                # 检查处理服务容器1
                elif "processor-server" in container_name.lower() and not "2" in container_name:
                    containers["processor"] = "running" if "Up" in status else "stopped"
                    if "Up" in status:
                        container_uptimes["processor"] = uptime_seconds
                
                # 检查处理服务容器2
                elif "processor-server-2" in container_name.lower() or "processor_server_2" in container_name.lower():
                    containers["processor2"] = "running" if "Up" in status else "stopped"
                    if "Up" in status:
                        container_uptimes["processor2"] = uptime_seconds
                
                # 检查Redis容器
                elif "redis" in container_name.lower():
                    containers["redis"] = "running" if "Up" in status else "stopped"
                    if "Up" in status:
                        container_uptimes["redis"] = uptime_seconds
                
                # 检查相机客户端容器1
                elif ("camera-client" in container_name.lower() or "camera_client" in container_name.lower()) and not "2" in container_name:
                    containers["camera_client"] = "running" if "Up" in status else "stopped"
                    if "Up" in status:
                        container_uptimes["camera_client"] = uptime_seconds
                
                # 检查相机客户端容器2
                elif "camera-client-2" in container_name.lower() or "camera_client_2" in container_name.lower():
                    containers["camera_client2"] = "running" if "Up" in status else "stopped"
                    if "Up" in status:
                        container_uptimes["camera_client2"] = uptime_seconds
                
                # 检查OpenPLC容器1
                elif ("openplc" in container_name.lower() or "control-belt" in container_name.lower()) and not "2" in container_name and not "control-belt-2" in container_name.lower():
                    containers["openplc"] = "running" if "Up" in status else "stopped"
                    if "Up" in status:
                        container_uptimes["openplc"] = uptime_seconds
                
                # 检查OpenPLC容器2
                elif "openplc2" in container_name.lower() or "control-belt-2" in container_name.lower():
                    containers["openplc2"] = "running" if "Up" in status else "stopped"
                    if "Up" in status:
                        container_uptimes["openplc2"] = uptime_seconds
    
    except Exception as e:
        print(f"检查Docker容器状态时出错: {e}")
        # 如果 Docker 命令失败，保持默认状态
        pass
    
    # 打印调试信息
    print(f"容器状态: {containers}")
    print(f"容器运行时间: {container_uptimes}")
    print(f"容器节点: {container_nodes}")
    
    return jsonify({
        "hostname": hostname,
        "os_info": os_info,
        "uptime": uptime,
        "containers": containers,
        "container_uptimes": container_uptimes,
        "container_nodes": container_nodes
    })

if __name__ == '__main__':
    # 启动 Redis 监听线程
    redis_thread = threading.Thread(target=redis_listener)
    redis_thread.daemon = True
    redis_thread.start()
    
    port = int(os.getenv('PORT', 5000))
    app.run(host='0.0.0.0', port=port, debug=True)
