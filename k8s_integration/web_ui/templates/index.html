<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Brics腾飞！</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            padding-top: 20px;
            background-color: #f5f5f7;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
            color: #1d1d1f;
            overflow-x: hidden; /* 防止水平滚动 */
        }
        
        .container {
            max-width: 1600px; /* 增加最大宽度以适应16:9显示器 */
            padding: 0 20px;
        }
        
        .card {
            border: none;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            overflow: hidden;
        }
        
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
        }
        
        .card-header {
            background-color: #fff;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
            padding: 15px 20px;
        }
        
        .card-header h5 {
            margin: 0;
            font-weight: 600;
            color: #1d1d1f;
        }
        
        .card-body {
            padding: 20px;
            background-color: #fff;
        }
        
        .detection-card, .performance-card {
            height: 100%;
            margin-bottom: 20px;
        }
        
        .defect-alert {
            color: white;
            background-color: #ff3b30;
            padding: 8px 15px;
            border-radius: 20px;
            display: inline-block;
            margin-top: 15px;
            font-weight: 500;
        }
        
        .no-defect {
            color: white;
            background-color: #34c759;
            padding: 8px 15px;
            border-radius: 20px;
            display: inline-block;
            margin-top: 15px;
            font-weight: 500;
        }
        
        .image-container {
            position: relative;
            margin-bottom: 20px;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
            aspect-ratio: 16/9; /* 设置16:9的宽高比 */
        }
        
        .image-container img {
            width: 100%;
            height: 100%;
            object-fit: cover; /* 确保图像填充容器并保持比例 */
            display: block;
        }
        
        .chart-container {
            height: 220px; /* 增加图表高度 */
            margin-bottom: 20px;
            border-radius: 8px;
            overflow: hidden;
        }
        
        h1.text-center {
            font-weight: 700;
            margin-bottom: 30px;
            color: #1d1d1f;
        }
        
        .defect-item {
            margin: 8px 0;
            padding: 10px;
            background-color: rgba(255, 59, 48, 0.08);
            border-radius: 8px;
            font-size: 0.9rem;
            transition: transform 0.2s ease;
        }
        
        .defect-item:hover {
            transform: translateX(5px);
        }
        
        .defect-number {
            font-weight: 600;
            margin-right: 8px;
            color: #1d1d1f;
        }
        
        .defect-class {
            color: #ff3b30;
            font-weight: 600;
        }
        
        .defect-confidence {
            color: #86868b;
            font-size: 0.85rem;
            margin-left: 8px;
        }
        
        .badge {
            padding: 5px 10px;
            border-radius: 20px;
            font-weight: 500;
            font-size: 0.8rem;
        }
        
        .badge.bg-success {
            background-color: #34c759 !important;
        }
        
        .badge.bg-danger {
            background-color: #ff3b30 !important;
        }
        
        .badge.bg-warning {
            background-color: #ff9500 !important;
        }
        
        /* 添加背景图案 */
        .pattern-background {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: 
                radial-gradient(circle at 25px 25px, rgba(0, 122, 255, 0.05) 2%, transparent 0%),
                radial-gradient(circle at 75px 75px, rgba(0, 122, 255, 0.05) 2%, transparent 0%);
            background-size: 100px 100px;
            z-index: -1;
        }
        
        /* 容器运行时间样式 */
        .container-uptime {
            font-size: 1.00rem;
            color: #86868b;
            margin-left: 10px;
        }
        
        /* 节点信息样式 */
        .container-node {
            font-size: 1.00rem;
            color: #007AFF;
            margin-left: 5px;
        }
        
        /* 响应式调整 */
        @media (min-width: 1200px) {
            .row {
                display: flex;
                flex-wrap: wrap;
            }
            
            .col-md-4 {
                flex: 0 0 33.333333%;
                max-width: 33.333333%;
            }
        }
        
        /* 在较小屏幕上调整布局 */
        @media (max-width: 1199.98px) {
            .chart-container {
                height: 180px;
            }
        }
    </style>
</head>
<body>
    <!-- 添加背景图案 -->
    <div class="pattern-background"></div>
    <div class="container">
        <h1 class="text-center mb-4">支持攻击自恢复的工业智能化弹性调度系统</h1>
        
        <!-- 第一行：检测结果和主机信息 -->
        <div class="row mb-4">
            <!-- 产线1 -->
            <div class="col-md-4">
                <div class="card detection-card">
                    <div class="card-header">
                        <h5>产线1</h5>
                    </div>
                    <div class="card-body">
                        <div class="image-container">
                            <img id="belt1-image" src="/static/placeholder.jpg" alt="产线1图像">
                        </div>
                        <div id="belt1-status" class="no-defect">无缺陷</div>
                        <div id="belt1-defects" class="mt-2">
                            <!-- 这里将显示所有检测到的缺陷 -->
                        </div>
                        <div id="belt1-details" class="mt-2">
                            <p>相机ID: <span id="belt1-camera-id">-</span></p>
                            <p>检测时间: <span id="belt1-timestamp">-</span></p>
                            <p>推理模型: <span id="belt1-model">-</span></p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 产线2 -->
            <div class="col-md-4">
                <div class="card detection-card">
                    <div class="card-header">
                        <h5>产线2</h5>
                    </div>
                    <div class="card-body">
                        <div class="image-container">
                            <img id="belt2-image" src="/static/placeholder.jpg" alt="产线2图像">
                        </div>
                        <div id="belt2-status" class="no-defect">无缺陷</div>
                        <div id="belt2-defects" class="mt-2">
                            <!-- 这里将显示所有检测到的缺陷 -->
                        </div>
                        <div id="belt2-details" class="mt-2">
                            <p>相机ID: <span id="belt2-camera-id">-</span></p>
                            <p>检测时间: <span id="belt2-timestamp">-</span></p>
                            <p>推理模型: <span id="belt2-model">-</span></p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 主机容器信息 -->
            <div class="col-md-4">
                <div class="card detection-card">
                    <div class="card-header">
                        <h5>主机容器状态</h5>
                    </div>
                    <div class="card-body">
                        <div id="host-info" class="host-info">
                            <div class="mb-3">
                                <h6 style="font-size: 1.1rem;">系统信息</h6>
                                <p>主机名: <span id="hostname">-</span></p>
                                <p>操作系统: <span id="os-info">-</span></p>
                                <p>运行时间: <span id="uptime">-</span></p>
                            </div>
                            <div>
                                <h6 style="font-size: 1.1rem;">容器状态</h6>
                                <p>Web容器: <span id="webui-status" class="badge bg-success">运行中</span></p>
                                <p>计算容器1: <span id="processor-status" class="badge bg-success">运行中</span></p>
                                <p>计算容器2: <span id="processor2-status" class="badge bg-success">运行中</span></p>
                                <p>Redis容器: <span id="redis-status" class="badge bg-success">运行中</span></p>
                                <p>感知容器1: <span id="camera-client-status" class="badge bg-success">运行中</span></p>
                                <p>感知容器2: <span id="camera-client2-status" class="badge bg-success">运行中</span></p>
                                <p>OpenPLC容器1: <span id="openplc-status" class="badge bg-success">运行中</span></p>
                                <p>OpenPLC容器2: <span id="openplc2-status" class="badge bg-success">运行中</span></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 第二行：系统性能指标 -->
        <div class="row">
            <!-- CPU使用率 -->
            <div class="col-md-4">
                <div class="card performance-card">
                    <div class="card-header">
                        <h5>CPU使用率</h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="cpuChart"></canvas>
                        </div>
                        <p>当前使用率: <span id="cpu-usage">-</span>%</p>
                    </div>
                </div>
            </div>
            
            <!-- 产线1推理情况 -->
            <div class="col-md-4">
                <div class="card performance-card">
                    <div class="card-header">
                        <h5>产线1处理</h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="fpsChart"></canvas>
                        </div>
                        <p>FPS: <span id="belt1-fps"></span>-</span></p>
                        <p>推理时间: <span id="belt1-processing-time">-</span>ms</p>
                    </div>
                </div>
            </div>

            <!-- 产线2推理情况 -->
            <div class="col-md-4">
                <div class="card performance-card">
                    <div class="card-header">
                        <h5>产线2处理</h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="belt2Chart"></canvas>
                        </div>
                        <p>FPS: <span id="belt2-fps">-</span></p>
                        <p>推理时间: <span id="belt2-processing-time">-</span>ms</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 图表配置和初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 获取图表上下文
            const cpuChartCtx = document.getElementById('cpuChart').getContext('2d');
            const fpsChartCtx = document.getElementById('fpsChart').getContext('2d');
            const belt2ChartCtx = document.getElementById('belt2Chart').getContext('2d');
            
            // 确保上下文存在
            if (!cpuChartCtx || !fpsChartCtx || !belt2ChartCtx) {
                console.error('无法获取图表上下文');
                return;
            }
            
            console.log('初始化图表...');
            
            // CPU使用率图表
            window.cpuChart = new Chart(cpuChartCtx, {
                type: 'line',
                data: {
                    labels: Array(10).fill(''),
                    datasets: [{
                        label: 'CPU使用率 (%)',
                        data: Array(10).fill(null),
                        borderColor: 'rgba(52, 199, 89, 1)',
                        backgroundColor: 'rgba(52, 199, 89, 0.1)',
                        tension: 0.4,
                        borderWidth: 2,
                        pointRadius: 3,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100,
                            grid: {
                                color: 'rgba(0, 0, 0, 0.05)'
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            display: false
                        }
                    }
                }
            });
            
            // 产线1 FPS图表
            window.fpsChart = new Chart(fpsChartCtx, {
                type: 'line',
                data: {
                    labels: Array(10).fill(''),
                    datasets: [{
                        label: 'FPS',
                        data: Array(10).fill(null),
                        borderColor: 'rgba(255, 149, 0, 1)',
                        backgroundColor: 'rgba(255, 149, 0, 0.1)',
                        tension: 0.4,
                        borderWidth: 2,
                        pointRadius: 3,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: {
                                color: 'rgba(0, 0, 0, 0.05)'
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            display: false
                        }
                    }
                }
            });
            
            // 产线2推理时间图表
            window.belt2Chart = new Chart(belt2ChartCtx, {
                type: 'line',
                data: {
                    labels: Array(10).fill(''),
                    datasets: [{
                        label: '推理时间 (ms)',
                        data: Array(10).fill(null),
                        borderColor: 'rgba(0, 122, 255, 1)',
                        backgroundColor: 'rgba(0, 122, 255, 0.1)',
                        tension: 0.4,
                        borderWidth: 2,
                        pointRadius: 3,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: {
                                color: 'rgba(0, 0, 0, 0.05)'
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            display: false
                        }
                    }
                }
            });
            
            console.log('图表初始化完成');
            
            // 定期刷新数据
            setInterval(fetchDetections, 1000);
            setInterval(fetchPerformance, 2000);
            setInterval(fetchHostInfo, 5000);
            
            // 页面加载时立即获取数据
            fetchDetections();
            fetchPerformance();
            fetchHostInfo();
        });

        // 更新图表数据
        function updateChart(chart, label, value) {
            if (!chart || !chart.data) {
                console.error('图表未初始化:', chart);
                return;
            }
            
            console.log(`更新图表数据: ${label}, ${value}`);
            
            const maxDataPoints = 20;
            
            if (chart.data.labels.length >= maxDataPoints) {
                chart.data.labels.shift();
                chart.data.datasets[0].data.shift();
            }
            
            chart.data.labels.push(label);
            chart.data.datasets[0].data.push(value);
            chart.update('none'); // 使用 'none' 模式更新，减少动画开销
        }
        
        // 格式化时间戳
        function formatTimestamp(timestamp) {
            if (!timestamp) return '-';
            const date = new Date(timestamp);
            return date.toLocaleTimeString();
        }
        
        // 格式化运行时间
        function formatUptime(seconds) {
            if (!seconds || seconds <= 0) return '-';
            
            const days = Math.floor(seconds / 86400);
            const hours = Math.floor((seconds % 86400) / 3600);
            const minutes = Math.floor((seconds % 3600) / 60);
            
            let result = '';
            if (days > 0) result += `${days}天 `;
            if (hours > 0 || days > 0) result += `${hours}小时 `;
            result += `${minutes}分钟`;
            
            return result;
        }
        
        // 容器启动时间记录
        const containerStartTimes = {
            "web_ui": null,
            "processor": null,
            "redis": null,
            "camera_client": null,
            "openplc": null
        };
        
        // 定期获取检测结果
        function fetchDetections() {
            fetch('/api/latest_detections')
                .then(response => response.json())
                .then(data => {
                    // 更新产线1数据
                    const belt1 = data.belt1;
                    if (belt1) {
                        const detections = belt1.detections || [];
                        
                        if (detections.length > 0) {
                            document.getElementById('belt1-status').className = 'defect-alert';
                            document.getElementById('belt1-status').textContent = `检测到 ${detections.length} 个缺陷`;
                            
                            // 显示所有缺陷
                            const defectsHtml = detections.map((det, index) => {
                                return `<div class="defect-item">
                                            <span class="defect-number">${index + 1}.</span>
                                            <span class="defect-class">${det.class || 'unknown'}</span>
                                            <span class="defect-confidence">(${(det.confidence * 100).toFixed(1)}%)</span>
                                        </div>`;
                            }).join('');
                            
                            document.getElementById('belt1-defects').innerHTML = defectsHtml;
                        } else {
                            document.getElementById('belt1-status').className = 'no-defect';
                            document.getElementById('belt1-status').textContent = '无缺陷';
                            document.getElementById('belt1-defects').innerHTML = '';
                        }
                        
                        document.getElementById('belt1-camera-id').textContent = belt1.camera_id || '-';
                        document.getElementById('belt1-timestamp').textContent = formatTimestamp(belt1.timestamp);
                        document.getElementById('belt1-model').textContent = belt1.model_name || '-';
                        
                        // 如果有图像数据，更新图像
                        if (belt1.image) {
                            document.getElementById('belt1-image').src = `data:image/jpeg;base64,${belt1.image}`;
                        }
                        
                        // 更新产线1推理图表
                        if (belt1.timestamp && belt1.perf_info) {
                            const timeLabel = formatTimestamp(belt1.timestamp);
                            const inferenceTime = belt1.perf_info.total_time * 1000 || 0;
                            const fps = belt1.perf_info.fps || 0;
                            updateChart(fpsChart, timeLabel, fps);
                            document.getElementById('belt1-fps').textContent = fps.toFixed(1);
                            document.getElementById('belt1-processing-time').textContent = inferenceTime.toFixed(1);
                        }
                    }
                    
                    // 更新产线2数据
                    const belt2 = data.belt2;
                    if (belt2) {
                        const detections = belt2.detections || [];
                        
                        if (detections.length > 0) {
                            document.getElementById('belt2-status').className = 'defect-alert';
                            document.getElementById('belt2-status').textContent = `检测到 ${detections.length} 个缺陷`;
                            
                            // 显示所有缺陷
                            const defectsHtml = detections.map((det, index) => {
                                return `<div class="defect-item">
                                            <span class="defect-number">${index + 1}.</span>
                                            <span class="defect-class">${det.class || 'unknown'}</span>
                                            <span class="defect-confidence">(${(det.confidence * 100).toFixed(1)}%)</span>
                                        </div>`;
                            }).join('');
                            
                            document.getElementById('belt2-defects').innerHTML = defectsHtml;
                        } else {
                            document.getElementById('belt2-status').className = 'no-defect';
                            document.getElementById('belt2-status').textContent = '无缺陷';
                            document.getElementById('belt2-defects').innerHTML = '';
                        }
                        
                        document.getElementById('belt2-camera-id').textContent = belt2.camera_id || '-';
                        document.getElementById('belt2-timestamp').textContent = formatTimestamp(belt2.timestamp);
                        document.getElementById('belt2-model').textContent = belt2.model_name || '-';
                        
                        // 如果有图像数据，更新图像
                        if (belt2.image) {
                            document.getElementById('belt2-image').src = `data:image/jpeg;base64,${belt2.image}`;
                        }
                        
                        // 更新产线2推理图表
                        if (belt2.timestamp && belt2.perf_info) {
                            const timeLabel = formatTimestamp(belt2.timestamp);
                            const inferenceTime = belt2.perf_info.total_time * 1000 || 0;
                            const fps = belt2.perf_info.fps || 0;
                            updateChart(belt2Chart, timeLabel, inferenceTime);
                            document.getElementById('belt2-fps').textContent = fps.toFixed(1);
                            document.getElementById('belt2-processing-time').textContent = inferenceTime.toFixed(1);
                        }
                    }
                })
                .catch(error => console.error('获取检测结果出错:', error));
        }
        
        // 定期获取性能指标
        function fetchPerformance() {
            fetch('/api/performance')
                .then(response => response.json())
                .then(data => {
                    console.log('获取到性能数据:', data);
                    
                    // 更新性能指标
                    const cpuUsage = data.cpu_usage !== undefined ? data.cpu_usage : 0;
                    document.getElementById('cpu-usage').textContent = cpuUsage.toFixed(1);
                    
                    // 更新CPU图表
                    const timeLabel = formatTimestamp(data.timestamp);
                    updateChart(window.cpuChart, timeLabel, cpuUsage);
                })
                .catch(error => {
                    console.error('获取性能指标出错:', error);
                });
        }
        
        // 获取主机信息
        function fetchHostInfo() {
            fetch('/api/host_info')
                .then(response => response.json())
                .then(data => {
                    document.getElementById('hostname').textContent = data.hostname || '-';
                    document.getElementById('os-info').textContent = data.os_info || '-';
                    document.getElementById('uptime').textContent = data.uptime || '-';
                    
                    // 更新容器状态
                    if (data.containers) {
                        console.log("容器信息:", data.containers);
                        console.log("运行时间:", data.container_uptimes);
                        console.log("节点信息:", data.container_nodes);
                        
                        updateContainerStatus('webui-status', data.containers.web_ui, 'web_ui', 
                            data.container_uptimes?.web_ui, data.container_nodes?.web_ui);
                        updateContainerStatus('processor-status', data.containers.processor, 'processor', 
                            data.container_uptimes?.processor, data.container_nodes?.processor);
                        updateContainerStatus('processor2-status', data.containers.processor2 || data.containers.processor_2, 'processor2', 
                            data.container_uptimes?.processor2 || data.container_uptimes?.processor_2, 
                            data.container_nodes?.processor2 || data.container_nodes?.processor_2);
                        updateContainerStatus('redis-status', data.containers.redis, 'redis', 
                            data.container_uptimes?.redis, data.container_nodes?.redis);
                        updateContainerStatus('camera-client-status', data.containers.camera_client, 'camera_client', 
                            data.container_uptimes?.camera_client, data.container_nodes?.camera_client);
                        updateContainerStatus('camera-client2-status', data.containers.camera_client2 || data.containers.camera_client_2, 'camera_client2', 
                            data.container_uptimes?.camera_client2 || data.container_uptimes?.camera_client_2, 
                            data.container_nodes?.camera_client2 || data.container_nodes?.camera_client_2);
                        updateContainerStatus('openplc-status', data.containers.openplc, 'openplc', 
                            data.container_uptimes?.openplc, data.container_nodes?.openplc);
                        updateContainerStatus('openplc2-status', data.containers.openplc2 || data.containers.openplc_2, 'openplc2', 
                            data.container_uptimes?.openplc2 || data.container_uptimes?.openplc_2, 
                            data.container_nodes?.openplc2 || data.container_nodes?.openplc_2);
                    }
                })
                .catch(error => {
                    console.error('获取主机信息出错:', error);
                    // 如果API不存在，显示默认值
                    document.getElementById('hostname').textContent = '未知';
                    document.getElementById('os-info').textContent = '未知';
                    document.getElementById('uptime').textContent = '未知';
                });
        }
        
        // 更新容器状态显示
        function updateContainerStatus(elementId, status, containerName, uptime, nodeName) {
            const element = document.getElementById(elementId);
            
            // 移除旧的运行时间和节点元素
            const oldUptimeElement = document.getElementById(`${elementId}-uptime`);
            const oldNodeElement = document.getElementById(`${elementId}-node`);
            if (oldUptimeElement) oldUptimeElement.remove();
            if (oldNodeElement) oldNodeElement.remove();
            
            if (status === 'running') {
                element.className = 'badge bg-success';
                element.textContent = '运行中';
                
                // 计算运行时间
                let uptimeText = '';
                if (uptime) {
                    // 使用API提供的运行时间
                    uptimeText = formatUptime(uptime);
                    
                    // 添加运行时间
                    const uptimeElement = document.createElement('span');
                    uptimeElement.id = `${elementId}-uptime`;
                    uptimeElement.className = 'container-uptime';
                    uptimeElement.textContent = uptimeText;
                    element.parentNode.appendChild(uptimeElement);
                }
                
                // 添加节点信息
                if (nodeName && nodeName.trim() !== '') {
                    const nodeElement = document.createElement('span');
                    nodeElement.id = `${elementId}-node`;
                    nodeElement.className = 'container-node';
                    nodeElement.textContent = ` [${nodeName}]`;
                    element.parentNode.appendChild(nodeElement);
                }
            } else if (status === 'stopped') {
                element.className = 'badge bg-danger';
                element.textContent = '已停止';
            } else {
                element.className = 'badge bg-warning';
                element.textContent = '未知';
            }
        }
        
        // 定期刷新数据
        setInterval(fetchDetections, 1000);
        setInterval(fetchPerformance, 2000);
        setInterval(fetchHostInfo, 5000);
        
        // 页面加载时立即获取数据
        fetchDetections();
        fetchPerformance();
        fetchHostInfo();
    </script>
</body>
</html>
