# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: frame_streamer.proto
# Protobuf Python Version: 4.25.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x14\x66rame_streamer.proto\x12\x0e\x66rame_streamer\"\xb7\x01\n\x0c\x46rameRequest\x12\x12\n\nframe_data\x18\x01 \x01(\x0c\x12\x11\n\ttimestamp\x18\x02 \x01(\t\x12\x11\n\tcamera_id\x18\x03 \x01(\t\x12<\n\x08metadata\x18\x04 \x03(\x0b\x32*.frame_streamer.FrameRequest.MetadataEntry\x1a/\n\rMetadataEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\"5\n\rFrameResponse\x12\x0e\n\x06status\x18\x01 \x01(\t\x12\x14\n\x0cprocessed_at\x18\x02 \x01(\x03\"\xa0\x01\n\rProcessResult\x12\x11\n\tcamera_id\x18\x01 \x01(\t\x12\x17\n\x0f\x66rame_timestamp\x18\x02 \x01(\t\x12\x1b\n\x13processed_timestamp\x18\x03 \x01(\t\x12-\n\ndetections\x18\x04 \x03(\x0b\x32\x19.frame_streamer.Detection\x12\x17\n\x0fprocessed_image\x18\x05 \x01(\x0c\"c\n\tDetection\x12\r\n\x05label\x18\x01 \x01(\t\x12\x12\n\nconfidence\x18\x02 \x01(\x02\x12\t\n\x01x\x18\x03 \x01(\x05\x12\t\n\x01y\x18\x04 \x01(\x05\x12\r\n\x05width\x18\x05 \x01(\x05\x12\x0e\n\x06height\x18\x06 \x01(\x05\x32\xb6\x01\n\rFrameStreamer\x12M\n\x0cStreamFrames\x12\x1c.frame_streamer.FrameRequest\x1a\x1d.frame_streamer.FrameResponse(\x01\x12V\n\x13\x42idirectionalStream\x12\x1c.frame_streamer.FrameRequest\x1a\x1d.frame_streamer.ProcessResult(\x01\x30\x01\x62\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'frame_streamer_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  DESCRIPTOR._options = None
  _globals['_FRAMEREQUEST_METADATAENTRY']._options = None
  _globals['_FRAMEREQUEST_METADATAENTRY']._serialized_options = b'8\001'
  _globals['_FRAMEREQUEST']._serialized_start=41
  _globals['_FRAMEREQUEST']._serialized_end=224
  _globals['_FRAMEREQUEST_METADATAENTRY']._serialized_start=177
  _globals['_FRAMEREQUEST_METADATAENTRY']._serialized_end=224
  _globals['_FRAMERESPONSE']._serialized_start=226
  _globals['_FRAMERESPONSE']._serialized_end=279
  _globals['_PROCESSRESULT']._serialized_start=282
  _globals['_PROCESSRESULT']._serialized_end=442
  _globals['_DETECTION']._serialized_start=444
  _globals['_DETECTION']._serialized_end=543
  _globals['_FRAMESTREAMER']._serialized_start=546
  _globals['_FRAMESTREAMER']._serialized_end=728
# @@protoc_insertion_point(module_scope)
