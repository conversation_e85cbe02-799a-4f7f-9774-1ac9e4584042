camera_id: camera-1
source: 0
# NCNN模型路径
small_modelpath_ncnn_param: "/models/eopt_int8.param"
small_modelpath_ncnn_bin: "/models/eopt_int8.bin"
large_modelpath_ncnn_param: "/models/sopt.param"
large_modelpath_ncnn_bin: "/models/sopt.bin"
small_model_name: "v5lite_e_int8"
large_model_name: "v5lite_s"
classfile: "/models/label_names"
outfolder: "stream_results"
confThreshold: 0.25
nmsThreshold: 0.45
save_interval: 0
record: false
no_show: true
fps_limit: 30
use_large_model: true
cpu_usage_threshold: 0.7
# NCNN特定参数
num_threads: 4
use_gpu: false
use_int8_small: false
use_int8_large: true
target_size: 320