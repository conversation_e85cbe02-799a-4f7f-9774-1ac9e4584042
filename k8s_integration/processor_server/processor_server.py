# grpc_server.py
# gRPC 服务端，接收并处理视频帧，通过 Redis 发布结果

import threading
import time
from collections import deque
import base64
import json
import yaml
import redis
import numpy as np
import cv2
import os
import frame_streamer_pb2
import frame_streamer_pb2_grpc
from v5lite_stream import yolov5_lite
from v5lite_ncnn import YOLOv5LiteDetector
from datetime import datetime
import psutil 


def load_config():
    """加载配置文件"""
    with open("param.yaml", "r") as file:
        return yaml.safe_load(file)

class FrameService(frame_streamer_pb2_grpc.FrameStreamerServicer):
    def __init__(self, redis_host="localhost", redis_port=6379, redis_channel="video_frames"):
        self.lock = threading.Lock()
        self.queue = deque(maxlen=4) # 限制最大帧缓存数量
        self.stopped = False
        self.redis = redis.Redis(host=redis_host, port=redis_port, decode_responses=True)
        self.redis_channel = redis_channel
        self.detect_config = load_config()

        # 初始化NCNN检测器 - 小模型
        self.detector_small = YOLOv5LiteDetector(
            param_path=self.detect_config["small_modelpath_ncnn_param"],
            bin_path=self.detect_config["small_modelpath_ncnn_bin"],
            target_size=self.detect_config.get("target_size", 320),
            prob_threshold=self.detect_config["confThreshold"],
            nms_threshold=self.detect_config["nmsThreshold"],
            num_threads=self.detect_config.get("num_threads", 4),
            use_gpu=self.detect_config.get("use_gpu", False),
            model_type='e',
            use_int8=self.detect_config.get("use_int8_small", True)
        )
        self.detector_small.model_name = self.detect_config["small_model_name"]
        
        # 初始化NCNN检测器 - 大模型
        self.detector_large = YOLOv5LiteDetector(
            param_path=self.detect_config["large_modelpath_ncnn_param"],
            bin_path=self.detect_config["large_modelpath_ncnn_bin"],
            target_size=self.detect_config.get("target_size", 320),
            prob_threshold=self.detect_config["confThreshold"],
            nms_threshold=self.detect_config["nmsThreshold"],
            num_threads=self.detect_config.get("num_threads", 4),
            use_gpu=self.detect_config.get("use_gpu", False),
            model_type='s',
            use_int8=self.detect_config.get("use_int8_large", False)
        )
        self.detector_large.model_name = self.detect_config["large_model_name"]
        
        # 性能统计
        self.frame_count = 0
        self.total_process_time = 0
        self.fps_avg = 0

        # 动态模型选择参数
        self.use_large_model = self.detect_config["use_large_model"]
        self.cpu_usage_threshold = self.detect_config.get("cpu_usage_threshold", 0.7)
        self.current_cpu_usage = 0.0
        self.cpu_monitor_thread = threading.Thread(target=self._monitor_cpu_usage, daemon=True)
        self.cpu_monitor_thread.start()

    def _monitor_cpu_usage(self):
        """独立线程持续监控CPU使用率"""
        while not self.stopped:
            self.current_cpu_usage = psutil.cpu_percent(interval=0.5) / 100  # 转换为小数形式
            time.sleep(0.3)
    
    def get_cpu_usage(self):
        return self.current_cpu_usage
    
    def receive_frames(self, request_iterator):
        for request in request_iterator:
            with self.lock:
                self.queue.append(request)

    def process_frame(self, frame):
        """处理函数，动态选择模型"""
        cpu_usage = self.get_cpu_usage()

        if cpu_usage >= self.cpu_usage_threshold:
            self.use_large_model = False
            detector = self.detector_small
        else:
            self.use_large_model = True
            detector = self.detector_large
        
        # 执行检测
        start_time = time.time()
        result_frame, objects, perf_info = detector.detect(frame)
        process_time = time.time() - start_time
       
        # 构建检测结果
        detections = []
        for obj in objects:
            detections.append({
                "class": detector.class_names[obj.label] if obj.label < len(detector.class_names) else f"class{obj.label}",
                "confidence": float(obj.prob),
                "bbox": [int(obj.rect.x), int(obj.rect.y), int(obj.rect.w), int(obj.rect.h)]
            })
        perf_info = {
             "model_name": perf_info.get("model_name", detector.model_name),
            "total_time": process_time,
            "inference_time": perf_info.get("inference_time", 0),
            "preprocess_time": perf_info.get("preprocess_time", 0),
            "postprocess_time": perf_info.get("postprocess_time", 0),
            "fps": perf_info.get("fps", 0),
            "cpu_usage": cpu_usage,
            "use_large_model": self.use_large_model,
        }
        return result_frame, detections, perf_info
    
    def process_frame_test(self, frame):
        """示例处理函数"""
        # 这里可以添加OpenCV处理逻辑
        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
        # 图像压缩reduce size
        original_height, original_width = frame.shape[:2]
        gray = cv2.resize(gray, (original_width // 2, original_height // 2))
        print("process...")
        time.sleep(0.1)
        return gray, {}, {}
    
    def StreamFrames(self, request_iterator, context):
        receiver_thread = threading.Thread(target=self.receive_frames, args=(request_iterator,))
        receiver_thread.start()
        
        while not self.stopped or self.queue:
            with self.lock:
                frame_request = self.queue[-1] if self.queue else None

            if frame_request:
                # print("processing....")
                # print(f"queue_size: {len(self.queue)}")
                # 解码图像
                img_np = np.frombuffer(frame_request.frame_data, dtype=np.uint8)
                frame = cv2.imdecode(img_np, cv2.IMREAD_COLOR)
                # 处理图像
                result_frame, detections, perf_info = self.process_frame(frame)
                # print(f"create_timestamp: {frame_request.timestamp}")
                # print(f"detections:{detections}")
                # print(f"perf_info:{perf_info}")
                # 编码结果
                _, jpeg = cv2.imencode('.jpg', result_frame)
                base64_img = base64.b64encode(jpeg).decode()

                # Redis publish
                self.redis.publish(self.redis_channel, json.dumps({
                    "camera_id": frame_request.camera_id,
                    "image": base64_img,
                    "detections": detections,
                    "perf_info": perf_info,
                    "create_timestamp": frame_request.timestamp
                }))
            time.sleep(0.1)  # 控制处理速率
        return frame_streamer_pb2.FrameResponse(
            status=f"Processed {len(self.queue)} frames",
            processed_at=int(time.time()*1000)
        )

    def BidirectionalStream(self, request_iterator, context):
        receiver_thread = threading.Thread(target=self.receive_frames, args=(request_iterator,))
        receiver_thread.start()

        while not self.stopped or self.queue:
            with self.lock:
                frame_request = self.queue.pop() if self.queue else None

            if frame_request:
                # print("processing....")
                
                img_np = np.frombuffer(frame_request.frame_data, dtype=np.uint8)
                frame = cv2.imdecode(img_np, cv2.IMREAD_COLOR)

                result_frame, detections, perf_info = self.process_frame(frame)
                # print(f"detections:{detections}")
                # print(f"perf_info:{perf_info}")
                _, jpeg = cv2.imencode('.jpg', result_frame)
                base64_img = base64.b64encode(jpeg).decode()

                # Redis publish
                self.redis.publish("video_frames", json.dumps({
                    "camera_id": frame_request.camera_id,
                    "image": base64_img,
                    "detections": detections,
                    "perf_info": perf_info,
                    "create_timestamp": frame_request.timestamp
                }))

                yield frame_streamer_pb2.ProcessResult(
                    camera_id=frame_request.camera_id,
                    frame_timestamp=frame_request.timestamp,
                    processed_timestamp=now.isoformat(),
                    detections=[],
                    processed_image=jpeg.tobytes()
                )
            else:
                time.sleep(0.05)

        receiver_thread.join()

# gRPC server 启动部分
if __name__ == "__main__":
    import grpc
    from concurrent import futures
    redis_host = os.getenv("REDIS_HOST", "localhost")
    redis_port = int(os.getenv("REDIS_PORT", 6379))
    redis_channel = os.getenv("REDIS_channel", "video_frames")
    server = grpc.server(futures.ThreadPoolExecutor(max_workers=2))
    frame_service = FrameService(
        redis_host=redis_host,  # 替换为你的 Redis 地址
        redis_port=redis_port,  # 替换为你的 Redis 端口
        redis_channel=redis_channel  # 替换为你的 Redis 频道
    )
    frame_streamer_pb2_grpc.add_FrameStreamerServicer_to_server(
        frame_service, server
    )
    grpc_host = os.getenv("GRPC_HOST", "0.0.0.0")  # 默认监听所有 IPv4 地址
    grpc_port = os.getenv("GRPC_PORT", "50051")

    server.add_insecure_port(f"{grpc_host}:{grpc_port}")
    server.start()
    print(f"gRPC server running on port {grpc_port}")
    server.wait_for_termination()
