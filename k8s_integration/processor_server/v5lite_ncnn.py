#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import sys
import cv2
import time
import os
import numpy as np
import ncnn
import argparse
from typing import List, Tuple
from datetime import datetime


class Rect:
    """矩形框类"""
    def __init__(self, x=0, y=0, w=0, h=0):
        self.x = x
        self.y = y
        self.w = w
        self.h = h


class DetectObject:
    """检测对象类"""
    def __init__(self, label=0, prob=0, x=0, y=0, w=0, h=0):
        self.label = label
        self.prob = prob
        self.rect = Rect(x, y, w, h)


class YOLOv5LiteDetector:
    """YOLOv5-lite检测器"""
    
    def __init__(self, 
                 param_path: str,
                 bin_path: str,
                 target_size: int = 320,
                 prob_threshold: float = 0.25,
                 nms_threshold: float = 0.45,
                 num_threads: int = 4,
                 use_gpu: bool = False,
                 anchors_file: str = None,
                 model_type: str = 'e',
                 use_int8: bool = False):
        """
        初始化检测器
        
        Args:
            param_path: .param文件路径
            bin_path: .bin文件路径
            target_size: 输入图像尺寸
            prob_threshold: 置信度阈值
            nms_threshold: NMS阈值
            num_threads: 线程数
            use_gpu: 是否使用GPU
            anchors_file: 自定义锚点文件路径
            model_type: 模型类型，'e','s'
            use_int8: 是否使用INT8量化模型
        """
        self.param_path = param_path
        self.bin_path = bin_path
        self.target_size = target_size
        self.prob_threshold = prob_threshold
        self.nms_threshold = nms_threshold
        self.num_threads = num_threads
        self.use_gpu = use_gpu
        self.model_type = model_type
        self.use_int8 = use_int8
        
        # 图像预处理参数
        self.mean_vals = []
        self.norm_vals = [1/255.0, 1/255.0, 1/255.0]  # 归一化到[0,1]
        
        # 类别名称
        self.class_names = [
            "bruise", "laps", "scratches_1", "scratches_2"
        ]
        
        # 根据模型类型设置输出节点名称
        if self.model_type == 's':
            self.output_names = ["647", "659", "671"]  # s模型的输出节点
        else:  # 'e'
            self.output_names = ["579", "591", "603"]  # e模型的输出节点
        
        # 加载自定义锚点（如果提供）
        if anchors_file and os.path.exists(anchors_file):
            self.anchors = self._load_custom_anchors(anchors_file)
            print(f"已加载自定义锚点: {anchors_file}")
        else:
            # 默认锚点
            self.anchors = [
                # 小尺度特征图的锚点 (40x40)
                [[10, 13], [16, 30], [33, 23]],
                # 中尺度特征图的锚点 (20x20)
                [[30, 61], [62, 45], [59, 119]],
                # 大尺度特征图的锚点 (10x10)
                [[116, 90], [156, 198], [373, 326]]
            ]
        
        # 调试模式
        self.debug = False
        
        # 初始化网络
        self._init_network()

    def _load_custom_anchors(self, anchors_file):
        """加载自定义锚点文件"""
        with open(anchors_file, 'r') as f:
            lines = f.readlines()
        
        anchors = []
        for line in lines:
            line = line.strip()
            if not line:
                continue
            
            # 解析一行锚点
            anchor_line = []
            pairs = line.split('],')
            for pair in pairs:
                pair = pair.replace('[', '').replace(']', '').strip()
                if not pair:
                    continue
                w, h = map(int, pair.split(','))
                anchor_line.append([w, h])
            
            if anchor_line:
                anchors.append(anchor_line)
        
        return anchors
    
    def _init_network(self):
        """初始化ncnn网络"""
        self.net = ncnn.Net()
        
        # 设置INT8推理
        if self.use_int8:
            try:
                self.net.opt.use_int8_inference = True
                print("启用INT8推理")
            except AttributeError:
                print("警告: 当前NCNN版本不支持INT8推理选项，将使用默认推理模式")
        
        # 设置GPU加速
        self.net.opt.use_vulkan_compute = self.use_gpu
        
        # 设置线程数
        self.net.opt.num_threads = self.num_threads
        
        # 加载模型
        ret_param = self.net.load_param(self.param_path)
        ret_model = self.net.load_model(self.bin_path)
        
        if ret_param != 0:
            raise RuntimeError(f"Failed to load param file: {self.param_path}")
        if ret_model != 0:
            raise RuntimeError(f"Failed to load model file: {self.bin_path}")
        
        print(f"Model loaded successfully!")
        print(f"- Param: {self.param_path}")
        print(f"- Model: {self.bin_path}")
        print(f"- GPU: {self.use_gpu}")
        print(f"- INT8: {self.use_int8}")
        print(f"- Threads: {self.num_threads}")
    
    def preprocess(self, img: np.ndarray) -> Tuple[ncnn.Mat, float]:
        """
        图像预处理
        
        Args:
            img: 输入图像 (BGR格式)
            
        Returns:
            预处理后的ncnn.Mat和缩放比例
        """
        # 计算缩放比例
        h, w = img.shape[:2]
        scale = min(self.target_size / w, self.target_size / h)
        
        # 缩放图像
        new_w = int(w * scale)
        new_h = int(h * scale)
        resized_img = cv2.resize(img, (new_w, new_h))
        
        # 计算填充偏移，使图像居中
        pad_w = (self.target_size - new_w) // 2
        pad_h = (self.target_size - new_h) // 2
        
        # 创建目标尺寸的图像并居中填充
        padded_img = np.zeros((self.target_size, self.target_size, 3), dtype=np.uint8)
        padded_img[pad_h:pad_h+new_h, pad_w:pad_w+new_w] = resized_img
        
        # 保存填充信息，用于后处理
        self.pad_info = (pad_w, pad_h, new_w, new_h)
        
        # 尝试不同的像素类型常量
        try:
            # 尝试 PIXEL_BGR
            pixel_type = ncnn.Mat.PIXEL_BGR
            if self.debug:
                print("使用 PIXEL_BGR")
        except AttributeError:
            try:
                # 尝试 PixelType.BGR
                pixel_type = ncnn.Mat.PixelType.BGR
                if self.debug:
                    print("使用 PixelType.BGR")
            except AttributeError:
                try:
                    # 尝试 PixelType.PIXEL_BGR
                    pixel_type = ncnn.Mat.PixelType.PIXEL_BGR
                    if self.debug:
                        print("使用 PixelType.PIXEL_BGR")
                except AttributeError:
                    # 如果都不可用，使用数值 2 (通常 BGR = 2)
                    pixel_type = 2
                    if self.debug:
                        print("使用数值 2 作为BGR像素类型")
        
        # 转换为ncnn.Mat
        mat_in = ncnn.Mat.from_pixels(padded_img, pixel_type, self.target_size, self.target_size)
        
        # 应用归一化
        if self.mean_vals and self.norm_vals:
            mat_in.substract_mean_normalize(self.mean_vals, self.norm_vals)
        elif self.norm_vals:
            mat_in.substract_mean_normalize([], self.norm_vals)
        
        return mat_in, scale
    
    def postprocess(self, outputs: List[ncnn.Mat], scale: float, img_w: int, img_h: int, pad_w: int, pad_h: int) -> List[DetectObject]:
        """
        后处理
        
        Args:
            outputs: 网络输出
            scale: 缩放比例
            img_w: 原图宽度
            img_h: 原图高度
            pad_w: 水平填充偏移
            pad_h: 垂直填充偏移
            
        Returns:
            检测结果列表
        """
        objects = []
        
        # 定义三个特征图的尺寸
        strides = [8, 16, 32]  # 对应40x40, 20x20, 10x10的特征图
        
        # 处理YOLOv5的三个检测头输出
        for i, output in enumerate(outputs):
            output_array = np.array(output)
            
            if self.debug:
                print(f"输出 {i} 形状: {output_array.shape}")
                if len(output_array.shape) > 0:
                    print(f"输出 {i} 前几个值: {output_array.flatten()[:10]}")
            
            # 确保输出形状正确 (anchors, grid_points, attrs)
            if len(output_array.shape) != 3:
                if self.debug:
                    print(f"警告: 输出 {i} 形状不是预期的3维，跳过")
                continue
                
            num_anchors, num_grid_points, num_attrs = output_array.shape
            
            # 确保属性数量正确 (x,y,w,h,conf,class1,class2,class3)
            if num_attrs != 5 + len(self.class_names):
                if self.debug:
                    print(f"警告: 输出 {i} 属性数量 {num_attrs} 不匹配预期的 {5 + len(self.class_names)}")
            
            # 计算特征图尺寸
            grid_size = int(np.sqrt(num_grid_points))
            stride = strides[i] if i < len(strides) else self.target_size / grid_size
            
            if self.debug:
                print(f"输出 {i} 网格尺寸: {grid_size}x{grid_size}, 步长: {stride}")
            
            # 处理每个预测框
            for anchor_idx in range(num_anchors):
                for grid_idx in range(num_grid_points):
                    # 获取当前预测框
                    pred = output_array[anchor_idx, grid_idx]
                    
                    # 应用sigmoid到objectness分数
                    obj_conf = self._sigmoid(pred[4])
                    
                    # 如果置信度低于阈值，跳过
                    if obj_conf < self.prob_threshold:
                        continue
                    
                    # 应用sigmoid到类别分数
                    class_scores = [self._sigmoid(x) for x in pred[5:5+len(self.class_names)]]
                    class_id = np.argmax(class_scores)
                    class_conf = class_scores[class_id]
                    
                    # 计算最终置信度
                    confidence = obj_conf * class_conf
                    
                    # 如果最终置信度低于阈值，跳过
                    if confidence < self.prob_threshold:
                        continue
                    
                    # 计算网格坐标
                    grid_x = grid_idx % grid_size
                    grid_y = grid_idx // grid_size
                    
                    # 解码边界框坐标 (参考v5lite-s.cpp中的计算方式)
                    # 应用sigmoid到x,y,w,h
                    x = self._sigmoid(pred[0])
                    y = self._sigmoid(pred[1])
                    w = self._sigmoid(pred[2])
                    h = self._sigmoid(pred[3])
                    
                    # 计算中心点坐标
                    cx = (x * 2.0 - 0.5 + grid_x) * stride
                    cy = (y * 2.0 - 0.5 + grid_y) * stride
                    
                    # 计算宽高 (参考v5lite-s.cpp中的计算方式)
                    # 这里使用了pow(w * 2.0, 2)而不是直接使用w
                    width = pow(w * 2.0, 2) * self.anchors[i][anchor_idx][0]
                    height = pow(h * 2.0, 2) * self.anchors[i][anchor_idx][1]
                    
                    # 转换为左上角坐标
                    x1 = cx - width / 2
                    y1 = cy - height / 2
                    
                    # 从填充图像坐标转换回原始图像坐标
                    x1 = (x1 - pad_w) / scale
                    y1 = (y1 - pad_h) / scale
                    w = width / scale
                    h = height / scale
                    
                    # 边界检查
                    if x1 < 0:
                        w += x1
                        x1 = 0
                    if y1 < 0:
                        h += y1
                        y1 = 0
                    if x1 + w > img_w:
                        w = img_w - x1
                    if y1 + h > img_h:
                        h = img_h - y1
                    
                    # 如果宽度或高度为负或太小，跳过
                    if w <= 1 or h <= 1:
                        continue
                    
                    objects.append(DetectObject(class_id, confidence, x1, y1, w, h))

        # 应用NMS
        objects = self.nms(objects)
        
        return objects

    def _sigmoid(self, x):
        """Sigmoid函数"""
        return 1.0 / (1.0 + np.exp(-x))
    
    def nms(self, objects: List[DetectObject]) -> List[DetectObject]:
        """非极大值抑制"""
        if len(objects) == 0:
            return []
        
        # 按置信度排序
        objects.sort(key=lambda x: x.prob, reverse=True)
        
        keep = []
        while objects:
            # 取置信度最高的
            current = objects.pop(0)
            keep.append(current)
            
            # 计算IoU并过滤
            objects = [obj for obj in objects if self.calculate_iou(current, obj) < self.nms_threshold]
        
        return keep
    
    def calculate_iou(self, obj1: DetectObject, obj2: DetectObject) -> float:
        """计算两个检测框的IoU"""
        # 计算交集
        x1 = max(obj1.rect.x, obj2.rect.x)
        y1 = max(obj1.rect.y, obj2.rect.y)
        x2 = min(obj1.rect.x + obj1.rect.w, obj2.rect.x + obj2.rect.w)
        y2 = min(obj1.rect.y + obj1.rect.h, obj2.rect.y + obj2.rect.h)
        
        if x2 <= x1 or y2 <= y1:
            return 0.0
        
        intersection = (x2 - x1) * (y2 - y1)
        area1 = obj1.rect.w * obj1.rect.h
        area2 = obj2.rect.w * obj2.rect.h
        union = area1 + area2 - intersection
        
        return intersection / union if union > 0 else 0.0
    
    def detect(self, img: np.ndarray):
        """
        执行检测
        
        Args:
            img: 输入图像 (BGR格式)
            
        Returns:
            tuple: (处理后的图像, 检测结果列表, 性能信息字典)
        """
        srcimg = img.copy()
        img_h, img_w = img.shape[:2]
        
        t_start = time.time()
        t_preprocess_start = time.time()
        # 预处理
        mat_in, scale = self.preprocess(img)
        t_preprocess = time.time()-t_preprocess_start
        
        # 推理
        t_inference_start = time.time()
        ex = self.net.create_extractor()
        ex.input("images", mat_in)  # 输入层名称为"images"
        
        # 尝试提取输出节点
        outputs = []
        
        for name in self.output_names:
            try:
                ret, out = ex.extract(name)
                if ret == 0:  # 成功提取
                    outputs.append(out)
                    if self.debug:
                        print(f"成功提取输出节点: {name}")
                else:
                    if self.debug:
                        print(f"提取输出节点 {name} 失败，返回码: {ret}")
            except Exception as e:
                if self.debug:
                    print(f"提取输出节点 {name} 失败: {e}")
        
        if not outputs:
            print("无法提取任何已知的输出节点，请检查模型结构")
            return srcimg, [], {"error": "无法提取输出节点"}
        t_inference = time.time() - t_inference_start

        # 后处理 - 使用保存的填充信息
        t_postprocess_start = time.time()
        pad_w, pad_h, new_w, new_h = self.pad_info
        objects = self.postprocess(outputs, scale, img_w, img_h, pad_w, pad_h)
        t_postprocess = time.time() - t_postprocess_start

        t_total = time.time() - t_start

        # 构建性能信息字典
        performance_info = {
            'total_time': t_total,
            'preprocess_time': t_preprocess,
            'inference_time': t_inference,
            'postprocess_time': t_postprocess,
            'fps': 1.0 / t_total if t_total > 0 else 0,
            'model_name': getattr(self, 'model_name', f"YOLOv5-lite-{self.model_type}")
        }
        
        # 绘制检测结果到图像上
        result_img = self.draw_objects(srcimg, objects)
        
        return result_img, objects, performance_info

    def draw_objects(self, img: np.ndarray, objects: List[DetectObject]):
        """
        绘制检测结果到图像上
        
        Args:
            img: 输入图像
            objects: 检测结果列表
        
        Returns:
            np.ndarray: 绘制了检测结果的图像
        """
        result_img = img.copy()
        
        # 为不同类别使用不同颜色
        colors = [
            (0, 255, 0), (0, 0, 255), (255, 0, 0), 
            (255, 255, 0), (0, 255, 255), (255, 0, 255),
            (128, 0, 0), (0, 128, 0), (0, 0, 128)
        ]
        
        for obj in objects:
            # 获取边界框坐标
            x1, y1 = int(obj.rect.x), int(obj.rect.y)
            x2, y2 = int(obj.rect.x + obj.rect.w), int(obj.rect.y + obj.rect.h)
            
            # 获取类别ID和置信度
            class_id = obj.label
            confidence = obj.prob
            
            # 选择颜色
            color = colors[class_id % len(colors)]
            
            # 绘制边界框
            cv2.rectangle(result_img, (x1, y1), (x2, y2), color, 2)
            
            # 准备标签文本
            if class_id < len(self.class_names):
                class_name = self.class_names[class_id]
            else:
                class_name = f"class{class_id}"
                
            label = f'{class_name}: {confidence:.2f}'
            
            # 获取文本大小
            labelSize, baseLine = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.5, 1)
            
            # 确保标签位置在图像内
            y1_label = max(y1, labelSize[1])
            
            # 绘制标签背景
            cv2.rectangle(result_img, 
                         (x1, y1_label - labelSize[1] - 10), 
                         (x1 + labelSize[0], y1_label), 
                         (255, 255, 255), 
                         cv2.FILLED)
            
            # 绘制标签文本
            cv2.putText(result_img, label, (x1, y1_label - 5), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 0), 1)
            
            print(f"Detected: {label} at ({x1}, {y1}, {x2-x1}, {y2-y1})")
        
        return result_img


def main():
    parser = argparse.ArgumentParser(description='YOLOv5-lite Inference with ncnn')
    parser.add_argument('--model_param', default='./sopt_new.param', help='Path to .param file')
    parser.add_argument('--model_bin', default='./sopt_new.bin', help='Path to .bin file')
    parser.add_argument('--image', default='./images/100022.jpg', help='Path to input image')
    parser.add_argument('--target_size', type=int, default=320, help='Input size')
    parser.add_argument('--prob_threshold', type=float, default=0.3, help='Confidence threshold')
    parser.add_argument('--nms_threshold', type=float, default=0.45, help='NMS threshold')
    parser.add_argument('--num_threads', type=int, default=4, help='Number of threads')
    parser.add_argument('--use_gpu', action='store_true', help='Use GPU acceleration')
    parser.add_argument('--use_int8', action='store_true', help='Use INT8 quantized model')
    parser.add_argument('--output', help='Output image path (optional)')
    parser.add_argument('--debug', action='store_true', help='Enable debug mode')
    parser.add_argument('--model_type', default='e', choices=['s', 'e'], help='Model type: s (small) or e (ghost)')
    parser.add_argument('--output_nodes', help='Comma-separated output node names to try (overrides model_type)')
    parser.add_argument('--anchors', help='Path to custom anchors file')
    
    args = parser.parse_args()
    
    # 读取图像
    img = cv2.imread(args.image)
    if img is None:
        print(f"Failed to load image: {args.image}")
        return
    
    # 创建检测器
    detector = YOLOv5LiteDetector(
        param_path=args.model_param,
        bin_path=args.model_bin,
        target_size=args.target_size,
        prob_threshold=args.prob_threshold,
        nms_threshold=args.nms_threshold,
        num_threads=args.num_threads,
        use_gpu=args.use_gpu,
        anchors_file=args.anchors,
        model_type=args.model_type,
        use_int8=args.use_int8
    )
    
    # 设置调试模式
    detector.debug = args.debug
    
    # 设置输出节点名称（如果指定）
    if args.output_nodes:
        detector.output_names = args.output_nodes.split(',')
    
    # 执行检测
    start_time = time.time()
    result_img, objects, performance_info = detector.detect(img)
    end_time = time.time()
    
     # 打印性能信息
    print("\n" + "="*50)
    print("检测性能:")
    print(f"模型名称: {performance_info['model_name']}")
    print(f"总检测时间: {performance_info['total_time']*1000:.2f}ms")
    print(f"预处理时间: {performance_info['preprocess_time']*1000:.2f}ms")
    print(f"推理时间: {performance_info['inference_time']*1000:.2f}ms")
    print(f"后处理时间: {performance_info['postprocess_time']*1000:.2f}ms")
    print(f"FPS: {performance_info['fps']:.2f}")
    print(f"检测到 {len(objects)} 个对象")
    for obj in objects:
        x1, y1 = int(obj.rect.x), int(obj.rect.y)
        x2, y2 = int(obj.rect.x + obj.rect.w), int(obj.rect.y + obj.rect.h)
            
        # 获取类别ID和置信度
        class_id = obj.label
        confidence = obj.prob
        class_name = detector.class_names[class_id]
        label = f'{class_name}: {confidence:.2f}'
        print(f"Detected: {label} at ({x1}, {y1}, {x2-x1}, {y2-y1})")
    print("="*50)

    # 在图像上添加性能信息
    h, w = result_img.shape[:2]
    info_text = [
        f"Model: {performance_info['model_name']}",
        f"FPS: {performance_info['fps']:.1f}",
        f"Inference: {performance_info['inference_time']*1000:.1f}ms",
        f"Total: {performance_info['total_time']*1000:.1f}ms"
    ]
     # 绘制半透明背景
    #overlay = result_img.copy()
    #cv2.rectangle(overlay, (10, 10), (300, 30 + 25 * len(info_text)), (0, 0, 0), -1)
    #cv2.addWeighted(overlay, 0.6, result_img, 0.4, 0, result_img)
    
    # 绘制文本
    for i, line in enumerate(info_text):
        y_pos = 30 + i * 25
        cv2.putText(result_img, line, (12, y_pos), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 0), 2)
        cv2.putText(result_img, line, (11, y_pos-1), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 1)

    # 添加时间戳
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    cv2.putText(result_img, timestamp, (10, result_img.shape[0] - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 0), thickness=2)
    cv2.putText(result_img, timestamp, (10, result_img.shape[0] - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), thickness=1)
    # 保存或显示结果
    if args.output:
        cv2.imwrite(args.output, result_img)
        print(f"Result saved to: {args.output}")
    else:
        cv2.imshow("YOLOv5-lite Detection", result_img)
        cv2.waitKey(0)
        cv2.destroyAllWindows()


if __name__ == "__main__":
    main()
