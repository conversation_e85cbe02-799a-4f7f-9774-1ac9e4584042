version: '3'

services:
  redis:
    image: redis:latest
    ports:
      - "6379:6379"
    networks:
      - detection-network

  processor-server:
    build:
      context: ./processor_server
      dockerfile: Dockerfile
    volumes:
      - ./processor_server:/app
      - ./models:/models
    environment:
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - GRPC_HOST=0.0.0.0
      - GRPC_PORT=50051
      - REDIS_channel=video_channel_1
      - PYTHONUNBUFFERED=1
    ports:
      - "50051:50051"
    depends_on:
      - redis
    networks:
      - detection-network
    privileged: true
    # 移除了healthcheck部分

  processor-server-2:
    build:
      context: ./processor_server
      dockerfile: Dockerfile
    volumes:
      - ./processor_server:/app
      - ./models:/models
    environment:
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - GRPC_HOST=0.0.0.0
      - GRPC_PORT=50052
      - REDIS_channel=video_channel_2
    ports:
      - "50052:50052"
    depends_on:
      - redis
    networks:
      - detection-network
    privileged: true

  camera-client:
    build:
      context: ./camera_client
      dockerfile: Dockerfile
    volumes:
      - ./camera_client:/app
      - /dev/video0:/dev/video0
    environment:
      - GRPC_HOST=processor-server
      - GRPC_PORT=50051
    depends_on:
      - processor-server
    networks:
      - detection-network
    privileged: true

  camera-client-2:
    build:
      context: ./camera_client
      dockerfile: Dockerfile
    volumes:
      - ./camera_client:/app
      - /dev/video2:/dev/video2
      - ./camera_client/param2.yaml:/app/param.yaml
    environment:
      - GRPC_HOST=processor-server-2
      - GRPC_PORT=50052
    depends_on:
      - processor-server-2
    networks:
      - detection-network
    privileged: true

  web-ui:
    build:
      context: ./web_ui
      dockerfile: Dockerfile
    volumes:
      - ./web_ui:/app
    environment:
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - FLASK_DEBUG=1
    ports:
      - "5000:5000"
    depends_on:
      - redis
      - processor-server
    networks:
      - detection-network

  ws-server:
    build:
      context: ./ws_server
      dockerfile: Dockerfile
    volumes:
      - ./ws_server:/app
      - /var/run/docker.sock:/var/run/docker.sock
    environment:
      - REDIS_HOST_1=redis
      - REDIS_PORT_1=6379
      - ARM_HOST=*************
    ports:
      - "8000:8000"
    depends_on:
      - redis
    networks:
      - detection-network

networks:
  detection-network:
    driver: bridge
