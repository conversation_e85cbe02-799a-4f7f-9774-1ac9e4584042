<!DOCTYPE html>
<style>

.container {
  display: flex;
  align-items: center;    /* 水平居中对齐 */
  gap: 20px;
  margin-top: 10px;
  /* width: 640px; */
  height: 480px; /* 设置容器高度 */
}

.container img {
  height: auto;           /* 保持图片比例 */
}

.container .results {
  margin-top: 10px;
  font-size: 30px; /* 设置字体大小 */
  /* 加粗 */
  font-weight: bold; /* 设置字体粗细 */
  color: #333;     /* 设置字体颜色 */
  display: flex;          /* 内部也使用Flex布局 */
  flex-direction: column; /* 让p标签垂直排列 */
  justify-content: center; /* 垂直居中 */
  text-align: left;
}
</style>
<html>
<body>
  <div class="container">
    <img id="videoStream_1" src="image/img1.png" width="640">
    <canvas id="delayChart1" style="width: 640px; height: 480px;"></canvas>
    <div id="stats_1" class="results">
      <div>产线1：</div>
      <div id="result1">检测缺陷类型</div>
    </div>
  </div>
  
  
  <div class="container">
    <img id="videoStream_2" src="image/img1.png" width="640">
    <canvas id="delayChart2" style="width: 640px; height: 480px;"></canvas>
    <div id="stats_2" class="results">
      <div>产线2：</div>
      <div id="result2">检测缺陷类型</div>
    </div>
  </div>

  <div class="container">
    <video id="video" autoplay playsinline muted style="width:640px; height:480px; background:black"></video>
    <div class="results">
      <div>钢材分拣区</div>
    </div>

  <script src="js/chart.js"></script>

  <script>
    const pc = new RTCPeerConnection();

    // ❗ 添加一个 recvonly 的 transceiver，这非常关键
    pc.addTransceiver('video', { direction: 'recvonly' });

    // pc.ontrack = (event) => {
        
    //   document.getElementById('video').srcObject = event.streams[0];
    // };
    pc.ontrack = (event) => {
        console.log("✅ Got remote track:", event.track);
        const [stream] = event.streams;
        const video = document.getElementById('video');
      video.srcObject = stream;

      // 🔧 添加这段，确保开始播放
      video.onloadedmetadata = () => {
          video.play().catch(err => console.error("❌ 播放失败:", err));
      };
    };


    /*async function negotiate() {
      const offer = await pc.createOffer();
      await pc.setLocalDescription(offer);

      const response = await fetch('http://127.0.0.1:8080/offer', {
        method: 'POST',
        body: JSON.stringify(pc.localDescription),
        headers: {
          'Content-Type': 'application/json'
        }
      });

      const answer = await response.json();
      await pc.setRemoteDescription(answer);
    }
    */
    negotiate();
  </script>

  <script>
    const maxLength = 200;
    let delay_time1 = [];
    let delay_time2 = [];
    let delayChart1;
    let delayChart2;
    let max
    function addData(delay_time, newValue) {
        delay_time.push(newValue);

        if (delay_time.length > maxLength) {
            delay_time.shift();
        }

        const sum = delay_time.reduce((a, b) => a + b, 0);
        const avg = sum / delay_time.length;
        const max = Math.max(...delay_time);
        const min = Math.min(...delay_time);

        return {
            avg: avg.toFixed(2),
            max: max.toFixed(2),
            min: min.toFixed(2)
        };
    }

    function initChart1() {
        const ctx1 = document.getElementById('delayChart1').getContext('2d');
        delayChart1 = new Chart(ctx1, {
            type: 'line',
            data: {
                labels: Array(maxLength).fill(''),
                datasets: [{
                    label: '图像处理时间(ms)',
                    data: delay_time1,
                    borderColor: 'rgb(75, 192, 192)',
                    tension: 0.1,
                    fill: false
                }]
            },
            options: {
                responsive: true,
                animation: {
                    duration: 0
                },
                scales: {
                    y: {
                        suggestedMin: 0,
                        suggestedMax: 300
                    }
                }
            }
        });

    }

    function initChart2() {
      const ctx2 = document.getElementById('delayChart2').getContext('2d');

      delayChart2 = new Chart(ctx2, {
            type: 'line',
            data: {
                labels: Array(maxLength).fill(''),
                datasets: [{
                    label: '图像处理时间(ms)',
                    data: delay_time2,
                    borderColor: 'rgb(75, 192, 192)',
                    tension: 0.1,
                    fill: false
                }]
            },
            options: {
                responsive: true,
                animation: {
                    duration: 0
                },
                scales: {
                    y: {
                        suggestedMin: 0,
                        suggestedMax: 300
                    }
                }
            }
        });
    }

    function updateChart1() {
        if (!delayChart1) {
            initChart2();
            return;
        }
        delayChart1.data.datasets[0].data = delay_time1;
        delayChart1.update();
    }
    function updateChart2() {
        if (!delayChart2) {
            initChart2();
            return;
        }
        delayChart2.data.datasets[0].data = delay_time2;
        delayChart2.update();
    }
    // 模拟数据
    // setInterval(() => {
    //     const newValue = Math.random() * 300;
    //     addData(newValue);
    //     updateChart();
    // }, 100);

    // 初始化
    initChart1();
    initChart2();
  </script>
  
  <script>
    //const ws1 = new WebSocket('ws://*************:30080/ws/video1');
    //const ws2 = new WebSocket('ws://*************:30080/ws/video2');
    // const ws1 = new WebSocket('ws://127.0.0.1:8000/ws/video1');
    // const ws2 = new WebSocket('ws://127.0.0.1:8000/ws/video2');
    const ws1 = new WebSocket('ws://localhost:8000/ws/video1');
    const ws2 = new WebSocket('ws://localhost:8000/ws/video2');

    const img1 = document.getElementById('videoStream_1');
    const stats1 = document.getElementById('stats_1');
    const time1 = document.getElementById('time1');
    const img2 = document.getElementById('videoStream_2');
    const stats2 = document.getElementById('stats_2');
    const time2 = document.getElementById('time2');

    ws1.onopen = () => {
      console.log("WebSocket 1 connection established.");
    };
    ws1.onerror = (error) => {
      console.error("WebSocket 1 error:", error);
    };
    ws1.onmessage = (event) => {
      const data = JSON.parse(event.data);
      console.log("Received message:", data);
      // 显示图像
      img1.src = `data:image/jpeg;base64,${data.image}`;
      
      var camera_id = data.camera_id;
      var perf_info = data.perf_info;
      var detections = data.detections;
      var create_timestamp = data.create_timestamp;
      var stats;

      stats = addData(delay_time1, perf_info.total_time*1000);
      updateChart1();


      if (detections.length === 0){
        stats1.innerHTML = `
            <div>产线1</div>
            <div>camera_id: ${camera_id}</div>
            <div>推理模型: ${perf_info.model_name}</div>
            <div id="result2">检测缺陷类型: 无</div>
            <div>平均时间: ${stats.avg}</div>
            <div>最大时间: ${stats.max}</div>
            <div>最小时间: ${stats.min}</div>
          `;
      }
      else{
        stats1.innerHTML = `
          <div>产线1</div>
          <div>camera_id: ${camera_id}</div>
          <div>推理模型: ${perf_info.model_name}</div>
          <div id="result2">检测缺陷类型: <span style="color: red;">${detections[0].class}</span></div>
          <div>平均时间: ${stats.avg}</div>
          <div>最大时间: ${stats.max}</div>
          <div>最小时间: ${stats.min}</div>
        `;
      }

      // console.log("Received data:", data);

    };

    ws2.onopen = () => {
      console.log("WebSocket 2 connection established.");
    };
    ws2.onerror = (error) => {
      console.error("WebSocket 2 error:", error);
    };

    ws2.onmessage = (event) => {
      const data = JSON.parse(event.data);
      console.log("Received message:", data);
      // 显示图像
      img2.src = `data:image/jpeg;base64,${data.image}`;
      
      var camera_id = data.camera_id;
      var perf_info = data.perf_info;
      var detections = data.detections;
      var create_timestamp = data.create_timestamp;
      var stats;

      stats = addData(delay_time2, perf_info.total_time*1000);
      // console.log("delay_time2:", delay_time2);
      updateChart2();


      if (detections.length === 0){
        stats2.innerHTML = `
            <div>产线2</div>
            <div>camera_id: ${camera_id}</div>
            <div>推理模型: ${perf_info.model_name}</div>
            <div id="result2">检测缺陷类型: 无</div>
            <div>平均时间: ${stats.avg}</div>
            <div>最大时间: ${stats.max}</div>
            <div>最小时间: ${stats.min}</div>
        `;
      }
      else{
        stats2.innerHTML = `
            <div>产线2</div>
            <div>camera_id: ${camera_id}</div>
            <div>推理模型: ${perf_info.model_name}</div>
            <div id="result2">检测缺陷类型: <span style="color: red;">${detections[0].class}</span></div>
            <div>平均时间: ${stats.avg}</div>
            <div>最大时间: ${stats.max}</div>
            <div>最小时间: ${stats.min}</div>
        `;
      }

      // console.log("Received data:", data);
      
    };
  </script>
</body>
</html>