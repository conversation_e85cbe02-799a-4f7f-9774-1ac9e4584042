import threading
import time
import grpc
import frame_streamer_pb2
import frame_streamer_pb2_grpc
import cv2
import yaml
import os
from datetime import datetime
from collections import deque

# 共享队列
frame_queue = deque(maxlen=10)  # 限制大小防止内存爆炸
queue_lock = threading.Lock()

stop_event = threading.Event()  # 用于优雅停止

def ms_timestamp_to_iso(ms_timestamp: float) -> str:
    """
    将毫秒时间戳（float）转换为 ISO 8601 格式字符串，精确到毫秒。
    
    示例输出: "2025-05-31T12:34:56.789"
    """
    seconds = ms_timestamp / 1000  # 转为秒
    dt = datetime.utcfromtimestamp(seconds)
    return dt.isoformat(timespec='milliseconds')

def load_config():
    """加载配置文件"""
    with open("param.yaml", "r") as file:
        return yaml.safe_load(file)
    
# def frame_producer(rtsp_url):
#     cap = cv2.VideoCapture(rtsp_url)
#     while True:
#         ret, frame = cap.read()
#         if not ret:
#             print("Failed to read frame")
#             cap.release()
#             time.sleep(1)
#             cap = cv2.VideoCapture(rtsp_url)
#             continue

#         with queue_lock:
#             frame_queue.append(frame)

#         time.sleep(0.05)


# def generate_frames(camera_id):
#     while True:
#         if not frame_queue:
#             time.sleep(0.01)
#             continue

#         with queue_lock:
#             if frame_queue:
#                 frame = frame_queue[-1]

#         _, buffer = cv2.imencode('.jpg', frame, [cv2.IMWRITE_JPEG_QUALITY, 90])
#         yield frame_streamer_pb2.FrameRequest(
#             frame_data=buffer.tobytes(),
#             timestamp=ms_timestamp_to_iso(time.time() * 1000),
#             camera_id=camera_id,
#             metadata={
#                 "resolution": f"{frame.shape[1]}x{frame.shape[0]}",
#                 "format": "jpeg",
#                 "quality": "90"
#             }
#         )
#         time.sleep(0.01)  # 控制发送速率

def generate_frames(camera_id, rtsp_url):
    cap = cv2.VideoCapture(rtsp_url)
    while True:
        ret, frame = cap.read()
        if not ret:
            print("Failed to read frame")
            cap.release()
            time.sleep(1)
            cap = cv2.VideoCapture(rtsp_url)
            continue
        
        now = datetime.utcnow()
        print(f"[{camera_id}] Frame captured at {now.isoformat()}")
        _, buffer = cv2.imencode('.jpg', frame, [cv2.IMWRITE_JPEG_QUALITY, 90])
        yield frame_streamer_pb2.FrameRequest(
            frame_data=buffer.tobytes(),
            timestamp=ms_timestamp_to_iso(time.time() * 1000),
            camera_id=camera_id,
            metadata={
                "resolution": f"{frame.shape[1]}x{frame.shape[0]}",
                "format": "jpeg",
                "quality": "90"
            }
        )

        time.sleep(0.05)


def run(grpc_host, grpc_port, camera_id, rtsp_url):
    grpc_server = f'{grpc_host}:{grpc_port}'
    print(f"连接到 gRPC 服务器: {grpc_server}")
    
    # 添加重试逻辑
    max_retries = 10
    retry_delay = 5  # 秒
    
    for retry in range(max_retries):
        try:
            channel = grpc.insecure_channel(grpc_server)
            # 尝试建立连接
            grpc.channel_ready_future(channel).result(timeout=10)
            print(f"成功连接到 gRPC 服务器: {grpc_server}")
            
            # 连接成功后继续执行
            stub = frame_streamer_pb2_grpc.FrameStreamerStub(channel)
            try:
                response = stub.StreamFrames(
                    generate_frames(camera_id, rtsp_url),
                )
                print(f"Server response: {response.status}")
            except grpc.RpcError as e:
                print(f"gRPC error: {e.code()} - {e.details()}")
            break  # 成功执行后退出循环
            
        except (grpc.FutureTimeoutError, grpc.RpcError) as e:
            if retry < max_retries - 1:
                print(f"连接失败 (尝试 {retry+1}/{max_retries}): {e}")
                print(f"{retry_delay} 秒后重试...")
                time.sleep(retry_delay)
            else:
                print(f"达到最大重试次数 ({max_retries})，无法连接到 gRPC 服务器")
                raise

def run_bidirectional_stream():
    channel = grpc.insecure_channel('127.0.0.1:50051')
    stub = frame_streamer_pb2_grpc.FrameStreamerStub(channel)

    responses = stub.BidirectionalStream(generate_frames("camera-01"))

    for response in responses:
        print(f"[{response.camera_id}] timestamp={response.frame_timestamp}")
        for det in response.detections:
            print(f" - {det.label} ({det.confidence*100:.1f}%) at ({det.x},{det.y},{det.width}x{det.height})") 


if __name__ == '__main__':
    param = load_config()
    try:
        camera_id = param.get("camera_id", "camera-01")
        source = param.get("source", 0)  # 默认使用摄像头0
        
        # 如果source是数字，则视为摄像头ID
        if isinstance(source, int):
            rtsp_url = source
        else:
            rtsp_url = source  # 否则视为RTSP URL
            
        grpc_host = os.getenv("GRPC_HOST", "processor-server")
        grpc_port = os.getenv("GRPC_PORT", "50051")
        
        print(f"启动相机客户端:")
        print(f"- 相机ID: {camera_id}")
        print(f"- 视频源: {rtsp_url}")
        print(f"- gRPC服务器: {grpc_host}:{grpc_port}")
        
        run(grpc_host, grpc_port, camera_id, rtsp_url)
    except KeyboardInterrupt:
        print("Stopping...")
        stop_event.set()
        print("Stopped.")
