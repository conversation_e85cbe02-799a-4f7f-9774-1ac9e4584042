# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

import frame_streamer_pb2 as frame__streamer__pb2


class FrameStreamerStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.StreamFrames = channel.stream_unary(
                '/frame_streamer.FrameStreamer/StreamFrames',
                request_serializer=frame__streamer__pb2.FrameRequest.SerializeToString,
                response_deserializer=frame__streamer__pb2.FrameResponse.FromString,
                )
        self.BidirectionalStream = channel.stream_stream(
                '/frame_streamer.FrameStreamer/BidirectionalStream',
                request_serializer=frame__streamer__pb2.FrameRequest.SerializeToString,
                response_deserializer=frame__streamer__pb2.ProcessResult.FromString,
                )


class FrameStreamerServicer(object):
    """Missing associated documentation comment in .proto file."""

    def StreamFrames(self, request_iterator, context):
        """单向流式传输 (采集→处理)
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def BidirectionalStream(self, request_iterator, context):
        """双向流式传输 (采集⇄处理)
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_FrameStreamerServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'StreamFrames': grpc.stream_unary_rpc_method_handler(
                    servicer.StreamFrames,
                    request_deserializer=frame__streamer__pb2.FrameRequest.FromString,
                    response_serializer=frame__streamer__pb2.FrameResponse.SerializeToString,
            ),
            'BidirectionalStream': grpc.stream_stream_rpc_method_handler(
                    servicer.BidirectionalStream,
                    request_deserializer=frame__streamer__pb2.FrameRequest.FromString,
                    response_serializer=frame__streamer__pb2.ProcessResult.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'frame_streamer.FrameStreamer', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))


 # This class is part of an EXPERIMENTAL API.
class FrameStreamer(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def StreamFrames(request_iterator,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.stream_unary(request_iterator, target, '/frame_streamer.FrameStreamer/StreamFrames',
            frame__streamer__pb2.FrameRequest.SerializeToString,
            frame__streamer__pb2.FrameResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def BidirectionalStream(request_iterator,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.stream_stream(request_iterator, target, '/frame_streamer.FrameStreamer/BidirectionalStream',
            frame__streamer__pb2.FrameRequest.SerializeToString,
            frame__streamer__pb2.ProcessResult.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)
