# redis-service.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: redis
  namespace: test-system
spec:
  replicas: 1
  selector:
    matchLabels:
      app: redis
  template:
    metadata:
      labels:
        app: redis
    spec:
      containers:
        - name: redis
          image: m.daocloud.io/docker.io/library/redis:latest
          imagePullPolicy: IfNotPresent
          ports:
            - containerPort: 6379
      nodeSelector:
        computing_device: compute_node

---
apiVersion: v1
kind: Service
metadata:
  name: redis-service
  namespace: test-system
spec:
  selector:
    app: redis
  ports:
    - port: 6379
      targetPort: 6379

# ---
# # redis-service.yaml
# apiVersion: apps/v1
# kind: Deployment
# metadata:
#   name: redis-2
# spec:
#   replicas: 1
#   selector:
#     matchLabels:
#       app: redis-2
#   template:
#     metadata:
#       labels:
#         app: redis-2
#     spec:
#       containers:
#         - name: redis-2
#           image: m.daocloud.io/docker.io/library/redis:latest
#           imagePullPolicy: IfNotPresent
#           ports:
#             - containerPort: 6379
#       # nodeSelector:
#       #   sensing_device: sensing_node

# ---
# apiVersion: v1
# kind: Service
# metadata:
#   name: redis-service-2
# spec:
#   selector:
#     app: redis-2
#   ports:
#     - port: 6379
#       targetPort: 6379
