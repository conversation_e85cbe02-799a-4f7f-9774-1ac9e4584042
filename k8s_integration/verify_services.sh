#!/bin/bash

# 检查服务是否存在
echo "检查服务..."
kubectl get svc -n test-system

# 检查服务端点
echo -e "\n检查服务端点..."
kubectl get endpoints -n test-system

# 检查DNS解析
echo -e "\n检查DNS解析..."
# 找一个运行中的Pod
POD=$(kubectl get pods -n test-system -o jsonpath='{.items[0].metadata.name}')
echo "使用Pod $POD 进行测试"

# 在Pod中执行nslookup
kubectl exec -n test-system $POD -- sh -c "nslookup processor-service.test-system.svc.cluster.local || echo 'nslookup命令不可用'"
kubectl exec -n test-system $POD -- sh -c "nslookup processor-service-2.test-system.svc.cluster.local || echo 'nslookup命令不可用'"

# 如果nslookup不可用，尝试ping
kubectl exec -n test-system $POD -- sh -c "ping -c 1 processor-service || echo 'ping命令不可用'"
kubectl exec -n test-system $POD -- sh -c "ping -c 1 processor-service-2 || echo 'ping命令不可用'"

# 检查服务的详细信息
echo -e "\n检查服务详细信息..."
kubectl describe svc processor-service -n test-system
kubectl describe svc processor-service-2 -n test-system