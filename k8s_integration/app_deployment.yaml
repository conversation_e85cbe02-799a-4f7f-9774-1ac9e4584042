# ConfigMap: param.yaml 配置
apiVersion: v1
kind: ConfigMap
metadata:
  name: detector-config-1
data:
  param.yaml: |
    camera_id: camera-2
    source: 2
    small_modelpath: /models/v5lite_s.onnx
    large_modelpath: /models/v5lite_g.onnx
    small_model_name: v5lite_s
    large_model_name: v5lite_g
    classfile: /models/label_names
    outfolder: stream_results
    confThreshold: 0.2
    nmsThreshold: 0.5
    save_interval: 0
    record: false
    no_show: false
    fps_limit: 30
    use_large_model: true
---
# ConfigMap: param.yaml 配置
apiVersion: v1
kind: ConfigMap
metadata:
  name: detector-config-2
data:
  param.yaml: |
    camera_id: camera-0
    source: 0
    small_modelpath: /models/v5lite_s.onnx
    large_modelpath: /models/v5lite_g.onnx
    small_model_name: v5lite_s
    large_model_name: v5lite_g
    classfile: /models/label_names
    outfolder: stream_results
    confThreshold: 0.2
    nmsThreshold: 0.5
    save_interval: 0
    record: false
    no_show: false
    fps_limit: 30
    use_large_model: true

---
# processor-service.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: processor-service
spec:
  replicas: 1
  selector:
    matchLabels:
      app: processor-service
  template:
    metadata:
      labels:
        app: processor-service
    spec:
      containers:
        - name: processor
          image: crpi-v0s88r5y9kedn2bv.cn-shanghai.personal.cr.aliyuncs.com/zsns1/yds:processor_server_4
          imagePullPolicy: IfNotPresent
          env:
            - name: REDIS_HOST
              value: redis-service
            - name: REDIS_PORT
              value: "6379"
            - name: GRPC_HOST
              value: "0.0.0.0"
            - name: GRPC_PORT
              value: "50051"
            - name: REDIS_channel
              value: "video_channel_1"
          volumeMounts:
            - name: config-volume
              mountPath: /app/param.yaml
              subPath: param.yaml
            - name: model-volume
              mountPath: /models
          resources:
            limits:
              cpu: "2"
          securityContext:
            privileged: true
      volumes:
        - name: config-volume
          configMap:
            name: detector-config-1
        - name: model-volume
          hostPath:
            path: /yds/models  # 替换为实际模型路径
      nodeSelector:
        computing_device: compute_node1

---
apiVersion: v1
kind: Service
metadata:
  name: processor-service
spec:
  selector:
    app: processor-service
  ports:
    - port: 50051
      targetPort: 50051
      protocol: TCP
  type: NodePort

---
# image-capture.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: image-capture
spec:
  replicas: 1
  selector:
    matchLabels:
      app: image-capture
  template:
    metadata:
      labels:
        app: image-capture
    spec:
      containers:
        - name: capture
          image: crpi-v0s88r5y9kedn2bv.cn-shanghai.personal.cr.aliyuncs.com/zsns1/yds:camera_client_3
          imagePullPolicy: IfNotPresent
          env:
            - name: GRPC_HOST
              value: processor-service
            - name: GRPC_PORT
              value: "50051"
          resources:
            limits:
              cpu: "2"
          securityContext:
            privileged: true
          volumeMounts:
            - name: video
              mountPath: /dev/video0
              readOnly: true
            - name: config-volume
              mountPath: /app/param.yaml
              subPath: param.yaml
      volumes:
        - name: video
          hostPath:
            path: /dev/video0
        - name: config-volume
          configMap:
            name: detector-config-1
      # kubectl label nodes <node-name> <label-key>=<label-value>
      nodeSelector:
        sensing_device: sensing_node

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: processor-service-2
spec:
  replicas: 1
  selector:
    matchLabels:
      app: processor-service-2
  template:
    metadata:
      labels:
        app: processor-service-2
    spec:
      containers:
        - name: processor
          image: crpi-v0s88r5y9kedn2bv.cn-shanghai.personal.cr.aliyuncs.com/zsns1/yds:processor_server_4
          imagePullPolicy: IfNotPresent
          env:
            - name: REDIS_HOST
              value: redis-service
            - name: REDIS_PORT
              value: "6379"
            - name: GRPC_HOST
              value: "0.0.0.0"
            - name: GRPC_PORT
              value: "50052"  # 修改端口
            - name: REDIS_channel
              value: "video_channel_2"  # 修改频道
          volumeMounts:
            - name: config-volume
              mountPath: /app/param.yaml
              subPath: param.yaml
            - name: model-volume
              mountPath: /models
          resources:
            limits:
              cpu: "2"
          securityContext:
            privileged: true
      volumes:
        - name: config-volume
          configMap:
            name: detector-config-2  # 修改 configMap 名称（如有）
        - name: model-volume
          hostPath:
            path: /yds/models
      affinity:
        nodeAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
            - weight: 100
              preference:
                matchExpressions:
                  - key: computing_device
                    operator: In
                    values:
                      - compute_node3
---
apiVersion: v1
kind: Service
metadata:
  name: processor-service-2
spec:
  selector:
    app: processor-service-2
  ports:
    - port: 50052
      targetPort: 50052
      protocol: TCP
  type: NodePort


---
# image-capture.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: image-capture-2
spec:
  replicas: 1
  selector:
    matchLabels:
      app: image-capture-2
  template:
    metadata:
      labels:
        app: image-capture-2
    spec:
      containers:
        - name: capture
          image: crpi-v0s88r5y9kedn2bv.cn-shanghai.personal.cr.aliyuncs.com/zsns1/yds:camera_client_3
          imagePullPolicy: IfNotPresent
          env:
            - name: GRPC_HOST
              value: processor-service-2
            - name: GRPC_PORT
              value: "50052"
          resources:
            limits:
              cpu: "1"
          securityContext:
            privileged: true
          volumeMounts:
            - name: video
              mountPath: /dev/video2
              readOnly: true
            - name: config-volume
              mountPath: /app/param.yaml
              subPath: param.yaml
      volumes:
        - name: video
          hostPath:
            path: /dev/video2
        - name: config-volume
          configMap:
            name: detector-config-2
      # kubectl label nodes <node-name> <label-key>=<label-value>
      nodeSelector:
        sensing_device: sensing_node