#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
YOLOv5-Lite 树莓派5优化推理脚本
专为树莓派5设计的轻量级网络推理脚本，降低计算负担
支持网络摄像头、RTSP流等输入源
"""

import argparse
import time
import threading
import queue
from pathlib import Path
import json
import socket
from datetime import datetime
import psutil

import cv2
import torch
import numpy as np
from collections import deque

# 导入YOLOv5-Lite模块
from models.experimental import attempt_load
from utils.general import check_img_size, non_max_suppression, scale_coords, xyxy2xywh
from utils.torch_utils import select_device
from utils.plots import plot_one_box


class RaspberryPiDetector:
    """树莓派优化的YOLO检测器"""
    
    def __init__(self, weights, device='cpu', img_size=320, conf_thres=0.5, iou_thres=0.45):
        """
        初始化检测器
        
        Args:
            weights: 模型权重路径
            device: 设备类型 ('cpu' 推荐用于树莓派)
            img_size: 输入图像尺寸 (320推荐，降低计算量)
            conf_thres: 置信度阈值
            iou_thres: NMS IoU阈值
        """
        self.device = select_device(device)
        self.img_size = img_size
        self.conf_thres = conf_thres
        self.iou_thres = iou_thres
        
        # 加载模型
        print(f"Loading model on {self.device}...")
        self.model = attempt_load(weights, map_location=self.device)
        self.stride = int(self.model.stride.max())
        self.img_size = check_img_size(img_size, s=self.stride)
        
        # 获取类别名称
        self.names = self.model.module.names if hasattr(self.model, 'module') else self.model.names
        self.colors = [[np.random.randint(0, 255) for _ in range(3)] for _ in self.names]
        
        # 性能统计
        self.fps_counter = deque(maxlen=30)  # 保存最近30帧的FPS
        self.inference_times = deque(maxlen=30)  # 保存最近30帧的推理时间
        self.detection_count = 0
        self.frame_count = 0
        self.start_time = time.time()
        
        # 预热模型
        print("Warming up model...")
        dummy_img = torch.zeros(1, 3, self.img_size, self.img_size).to(self.device)
        with torch.no_grad():
            self.model(dummy_img)
        print("Model ready!")
    
    def preprocess_image(self, img0):
        """
        图像预处理 - 针对树莓派优化
        
        Args:
            img0: 原始图像 (BGR格式)
            
        Returns:
            img: 预处理后的tensor
            img0: 原始图像
        """
        # 调整图像尺寸
        img = cv2.resize(img0, (self.img_size, self.img_size))
        
        # BGR转RGB
        img = img[:, :, ::-1].transpose(2, 0, 1)  # BGR to RGB, HWC to CHW
        img = np.ascontiguousarray(img)
        
        # 转换为tensor
        img = torch.from_numpy(img).to(self.device)
        img = img.float() / 255.0  # 归一化
        
        if img.ndimension() == 3:
            img = img.unsqueeze(0)
            
        return img, img0
    
    def detect_frame(self, img0):
        """
        单帧检测

        Args:
            img0: 输入图像

        Returns:
            detections: 检测结果列表
            performance_info: 性能信息字典
        """
        t_start = time.time()

        # 预处理时间
        t_preprocess_start = time.time()
        img, img0 = self.preprocess_image(img0)
        t_preprocess_end = time.time()

        # 推理时间
        t_inference_start = time.time()
        with torch.no_grad():
            pred = self.model(img)[0]
        t_inference_end = time.time()

        # NMS时间
        t_nms_start = time.time()
        pred = non_max_suppression(pred, self.conf_thres, self.iou_thres)
        t_nms_end = time.time()

        t_total = time.time() - t_start

        # 计算各阶段时间
        preprocess_time = t_preprocess_end - t_preprocess_start
        inference_time = t_inference_end - t_inference_start
        nms_time = t_nms_end - t_nms_start
        
        # 处理检测结果
        detections = []
        for det in pred:
            if len(det):
                # 缩放坐标到原始图像尺寸
                det[:, :4] = scale_coords(img.shape[2:], det[:, :4], img0.shape).round()

                for *xyxy, conf, cls in det:
                    detection = {
                        'class_id': int(cls),
                        'class_name': self.names[int(cls)],
                        'confidence': float(conf),
                        'bbox': [int(x) for x in xyxy],  # [x1, y1, x2, y2]
                        'center': [(int(xyxy[0]) + int(xyxy[2])) // 2,
                                 (int(xyxy[1]) + int(xyxy[3])) // 2]
                    }
                    detections.append(detection)

        # 更新性能统计
        self.frame_count += 1
        self.inference_times.append(inference_time)

        # 构建性能信息
        performance_info = {
            'total_time': t_total,
            'preprocess_time': preprocess_time,
            'inference_time': inference_time,
            'nms_time': nms_time,
            'frame_count': self.frame_count,
            'avg_inference_time': np.mean(self.inference_times) if self.inference_times else 0
        }

        return detections, performance_info
    
    def draw_detections(self, img, detections):
        """
        在图像上绘制检测结果
        
        Args:
            img: 输入图像
            detections: 检测结果列表
            
        Returns:
            img: 绘制后的图像
        """
        for det in detections:
            x1, y1, x2, y2 = det['bbox']
            label = f"{det['class_name']} {det['confidence']:.2f}"
            color = self.colors[det['class_id']]
            
            # 绘制边界框
            cv2.rectangle(img, (x1, y1), (x2, y2), color, 2)
            
            # 绘制标签
            label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.5, 2)[0]
            cv2.rectangle(img, (x1, y1 - label_size[1] - 10), 
                         (x1 + label_size[0], y1), color, -1)
            cv2.putText(img, label, (x1, y1 - 5), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 2)
        
        return img
    
    def update_fps(self, total_time):
        """更新FPS统计"""
        fps = 1.0 / total_time if total_time > 0 else 0
        self.fps_counter.append(fps)
        return np.mean(self.fps_counter)

    def get_cpu_usage(self):
        """获取CPU使用率"""
        try:
            return psutil.cpu_percent(interval=None)
        except:
            return 0.0

    def get_memory_usage(self):
        """获取内存使用率"""
        try:
            memory = psutil.virtual_memory()
            return memory.percent
        except:
            return 0.0


class NetworkStreamer:
    """网络流处理器"""
    
    def __init__(self, source, buffer_size=2):
        """
        初始化网络流
        
        Args:
            source: 视频源 (摄像头ID、RTSP URL等)
            buffer_size: 缓冲区大小
        """
        self.source = source
        self.buffer_size = buffer_size
        self.frame_queue = queue.Queue(maxsize=buffer_size)
        self.cap = None
        self.running = False
        
    def start_capture(self):
        """开始捕获视频流"""
        self.cap = cv2.VideoCapture(self.source)
        
        # 设置缓冲区大小
        self.cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)
        
        # 针对网络流的优化设置
        if isinstance(self.source, str) and self.source.startswith(('rtsp://', 'http://')):
            self.cap.set(cv2.CAP_PROP_FPS, 15)  # 限制FPS降低网络负担
        
        if not self.cap.isOpened():
            raise ValueError(f"Cannot open video source: {self.source}")
        
        self.running = True
        self.capture_thread = threading.Thread(target=self._capture_frames)
        self.capture_thread.daemon = True
        self.capture_thread.start()
        
        print(f"Started capturing from: {self.source}")
    
    def _capture_frames(self):
        """后台线程捕获帧"""
        while self.running:
            ret, frame = self.cap.read()
            if not ret:
                print("Failed to read frame, reconnecting...")
                time.sleep(1)
                continue
            
            # 如果队列满了，丢弃旧帧
            if self.frame_queue.full():
                try:
                    self.frame_queue.get_nowait()
                except queue.Empty:
                    pass
            
            try:
                self.frame_queue.put(frame, timeout=0.1)
            except queue.Full:
                pass
    
    def get_frame(self):
        """获取最新帧"""
        try:
            return self.frame_queue.get(timeout=0.1)
        except queue.Empty:
            return None
    
    def stop(self):
        """停止捕获"""
        self.running = False
        if self.cap:
            self.cap.release()


def save_detection_log(detections, timestamp, log_file="detection_log.json"):
    """保存检测日志"""
    log_entry = {
        'timestamp': timestamp,
        'detection_count': len(detections),
        'detections': detections
    }
    
    try:
        with open(log_file, 'a') as f:
            f.write(json.dumps(log_entry) + '\n')
    except Exception as e:
        print(f"Failed to save log: {e}")


def main():
    parser = argparse.ArgumentParser(description='YOLOv5-Lite Raspberry Pi Detector')
    parser.add_argument('--weights', nargs='+', type=str, 
                       default=['runs/train/exp2/weights/best.pt', 'runs/train/exp3/weights/best.pt'],
                       help='model weights paths (supports multiple models)')
    parser.add_argument('--source', type=str, default='rtsp://***********:8554/stream0',
                       help='video source (0 for webcam, RTSP URL, etc.)')
    parser.add_argument('--img-size', type=int, default=320,
                       help='inference size (320 recommended for RPi)')
    parser.add_argument('--conf-thres', type=float, default=0.5,
                       help='confidence threshold')
    parser.add_argument('--iou-thres', type=float, default=0.45,
                       help='NMS IoU threshold')
    parser.add_argument('--device', default='cpu',
                       help='device (cpu recommended for RPi)')
    parser.add_argument('--view-img', action='store_true',
                       help='display results')
    parser.add_argument('--save-log', action='store_true',
                       help='save detection logs')
    parser.add_argument('--fps-limit', type=int, default=10,
                       help='FPS limit to reduce CPU load')
    
    args = parser.parse_args()
    
    # 转换source参数
    if args.source.isdigit():
        args.source = int(args.source)
    
    print("="*50)
    print("🍓 YOLOv5-Lite Raspberry Pi Multi-Model Detector")
    print("="*50)
    print(f"Models: {args.weights}")
    print(f"Source: {args.source}")
    print(f"Image size: {args.img_size}")
    print(f"Device: {args.device}")
    print(f"FPS limit: {args.fps_limit}")
    print("="*50)
    
    # 初始化两个检测器
    detector_models = []
    model_names = []
    
    for i, weight_path in enumerate(args.weights):
        print(f"Loading model {i+1}: {weight_path}")
        detector = RaspberryPiDetector(
            weights=weight_path,
            device=args.device,
            img_size=args.img_size,
            conf_thres=args.conf_thres,
            iou_thres=args.iou_thres
        )
        detector_models.append(detector)
        model_name = f"Model-{i+1} ({weight_path.split('/')[-3]})"  # 例如: Model-1 (exp2)
        model_names.append(model_name)
    
    # 当前使用的模型索引
    current_model_idx = 0
    
    # 初始化网络流
    streamer = NetworkStreamer(args.source)
    
    try:
        streamer.start_capture()
        
        frame_interval = 1.0 / args.fps_limit
        last_frame_time = 0
        
        print("Starting detection... Press 's' to switch models, 'q' to quit")
        
        while True:
            current_time = time.time()
            
            # FPS限制
            if current_time - last_frame_time < frame_interval:
                time.sleep(0.01)
                continue
            
            # 获取帧
            frame = streamer.get_frame()
            if frame is None:
                continue
            
            # 获取当前检测器
            current_detector = detector_models[current_model_idx]
            
            # 检测
            detections, performance_info = current_detector.detect_frame(frame)
            avg_fps = current_detector.update_fps(performance_info['total_time'])

            # 获取系统性能信息
            cpu_usage = current_detector.get_cpu_usage()
            memory_usage = current_detector.get_memory_usage()
            
            # 绘制结果
            if args.view_img or len(detections) > 0:
                result_frame = current_detector.draw_detections(frame.copy(), detections)

                # 添加详细性能信息和当前模型信息
                info_lines = [
                    f"Model: {model_names[current_model_idx]}",
                    f"FPS: {avg_fps:.1f} | Frame: {performance_info['frame_count']}",
                    f"Inference: {performance_info['inference_time']*1000:.1f}ms | Total: {performance_info['total_time']*1000:.1f}ms",
                    f"CPU: {cpu_usage:.1f}% | Memory: {memory_usage:.1f}%",
                    f"Detections: {len(detections)}"
                ]

                # 在图像上显示性能信息
                for i, line in enumerate(info_lines):
                    y_pos = 25 + i * 25
                    cv2.putText(result_frame, line, (10, y_pos),
                               cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)

                if args.view_img:
                    cv2.imshow('YOLOv5-Lite Multi-Model Detection', result_frame)
                    key = cv2.waitKey(1) & 0xFF
                    
                    # 处理按键
                    if key == ord('q'):
                        break
                    elif key == ord('s'):
                        # 切换模型
                        current_model_idx = (current_model_idx + 1) % len(detector_models)
                        print(f"🔄 Switched to {model_names[current_model_idx]}")
            
            # 保存检测日志
            if args.save_log and len(detections) > 0:
                timestamp = datetime.now().isoformat()
                save_detection_log(detections, timestamp)
            
            # 打印详细性能和检测信息
            timestamp = datetime.now().strftime('%H:%M:%S')

            # 基础性能信息（每帧都显示）
            print(f"[{timestamp}] {model_names[current_model_idx]} | "
                  f"Frame {performance_info['frame_count']:4d} | "
                  f"FPS: {avg_fps:5.1f} | "
                  f"Inference: {performance_info['inference_time']*1000:6.1f}ms | "
                  f"Total: {performance_info['total_time']*1000:6.1f}ms | "
                  f"CPU: {cpu_usage:5.1f}% | "
                  f"Memory: {memory_usage:5.1f}% | "
                  f"Detections: {len(detections)}")

            # 检测结果详情（仅当有检测时显示）
            if len(detections) > 0:
                detection_list = ', '.join([f"{d['class_name']}({d['confidence']:.2f})" for d in detections])
                print(f"    └─ Objects: {detection_list}")

                # 显示详细时间分解
                print(f"    └─ Timing: Preprocess={performance_info['preprocess_time']*1000:.1f}ms, "
                      f"Inference={performance_info['inference_time']*1000:.1f}ms, "
                      f"NMS={performance_info['nms_time']*1000:.1f}ms")
            
            last_frame_time = current_time
            current_detector.detection_count += len(detections)
    
    except KeyboardInterrupt:
        print("\nStopping detection...")
    
    except Exception as e:
        print(f"Error: {e}")
    
    finally:
        streamer.stop()
        cv2.destroyAllWindows()
        
        # 打印每个模型的总检测数
        print("\n" + "="*50)
        print("Detection Summary:")
        for i, detector in enumerate(detector_models):
            print(f"{model_names[i]}: {detector.detection_count} detections")
        print("="*50)
        print("Detection stopped.")


if __name__ == '__main__':
    main()
