Performing C SOURCE FILE Test CMAKE_HAVE_LIBC_PTHREAD failed with the following output:
Change Dir: /home/<USER>/Desktop/AI_Group/TensorRT/ChaucerG_TRT8_Demo/tensorrt_inference/yolov5-debug/build/CMakeFiles/CMakeTmp

Run Build Command(s):/usr/bin/make -f Makefile cmTC_07949/fast && /usr/bin/make  -f CMakeFiles/cmTC_07949.dir/build.make CMakeFiles/cmTC_07949.dir/build
make[1]: Entering directory '/home/<USER>/Desktop/AI_Group/TensorRT/ChaucerG_TRT8_Demo/tensorrt_inference/yolov5-debug/build/CMakeFiles/CMakeTmp'
Building C object CMakeFiles/cmTC_07949.dir/src.c.o
/usr/bin/cc -DCMAKE_HAVE_LIBC_PTHREAD  -fPIC  -o CMakeFiles/cmTC_07949.dir/src.c.o -c /home/<USER>/Desktop/AI_Group/TensorRT/ChaucerG_TRT8_Demo/tensorrt_inference/yolov5-debug/build/CMakeFiles/CMakeTmp/src.c
Linking C executable cmTC_07949
/usr/local/bin/cmake -E cmake_link_script CMakeFiles/cmTC_07949.dir/link.txt --verbose=1
/usr/bin/cc -fPIC  CMakeFiles/cmTC_07949.dir/src.c.o -o cmTC_07949 
CMakeFiles/cmTC_07949.dir/src.c.o: In function `main':
src.c:(.text+0x3e): undefined reference to `pthread_create'
src.c:(.text+0x4a): undefined reference to `pthread_detach'
src.c:(.text+0x56): undefined reference to `pthread_cancel'
src.c:(.text+0x67): undefined reference to `pthread_join'
src.c:(.text+0x7b): undefined reference to `pthread_atfork'
collect2: error: ld returned 1 exit status
CMakeFiles/cmTC_07949.dir/build.make:98: recipe for target 'cmTC_07949' failed
make[1]: *** [cmTC_07949] Error 1
make[1]: Leaving directory '/home/<USER>/Desktop/AI_Group/TensorRT/ChaucerG_TRT8_Demo/tensorrt_inference/yolov5-debug/build/CMakeFiles/CMakeTmp'
Makefile:127: recipe for target 'cmTC_07949/fast' failed
make: *** [cmTC_07949/fast] Error 2


Source file was:
#include <pthread.h>

static void* test_func(void* data)
{
  return data;
}

int main(void)
{
  pthread_t thread;
  pthread_create(&thread, NULL, test_func, NULL);
  pthread_detach(thread);
  pthread_cancel(thread);
  pthread_join(thread, NULL);
  pthread_atfork(NULL, NULL, NULL);
  pthread_exit(NULL);

  return 0;
}

Determining if the function pthread_create exists in the pthreads failed with the following output:
Change Dir: /home/<USER>/Desktop/AI_Group/TensorRT/ChaucerG_TRT8_Demo/tensorrt_inference/yolov5-debug/build/CMakeFiles/CMakeTmp

Run Build Command(s):/usr/bin/make -f Makefile cmTC_fb1af/fast && /usr/bin/make  -f CMakeFiles/cmTC_fb1af.dir/build.make CMakeFiles/cmTC_fb1af.dir/build
make[1]: Entering directory '/home/<USER>/Desktop/AI_Group/TensorRT/ChaucerG_TRT8_Demo/tensorrt_inference/yolov5-debug/build/CMakeFiles/CMakeTmp'
Building C object CMakeFiles/cmTC_fb1af.dir/CheckFunctionExists.c.o
/usr/bin/cc   -fPIC -DCHECK_FUNCTION_EXISTS=pthread_create -o CMakeFiles/cmTC_fb1af.dir/CheckFunctionExists.c.o -c /usr/local/share/cmake-3.21/Modules/CheckFunctionExists.c
Linking C executable cmTC_fb1af
/usr/local/bin/cmake -E cmake_link_script CMakeFiles/cmTC_fb1af.dir/link.txt --verbose=1
/usr/bin/cc -fPIC -DCHECK_FUNCTION_EXISTS=pthread_create CMakeFiles/cmTC_fb1af.dir/CheckFunctionExists.c.o -o cmTC_fb1af  -lpthreads 
/usr/bin/ld: cannot find -lpthreads
collect2: error: ld returned 1 exit status
CMakeFiles/cmTC_fb1af.dir/build.make:98: recipe for target 'cmTC_fb1af' failed
make[1]: *** [cmTC_fb1af] Error 1
make[1]: Leaving directory '/home/<USER>/Desktop/AI_Group/TensorRT/ChaucerG_TRT8_Demo/tensorrt_inference/yolov5-debug/build/CMakeFiles/CMakeTmp'
Makefile:127: recipe for target 'cmTC_fb1af/fast' failed
make: *** [cmTC_fb1af/fast] Error 2



