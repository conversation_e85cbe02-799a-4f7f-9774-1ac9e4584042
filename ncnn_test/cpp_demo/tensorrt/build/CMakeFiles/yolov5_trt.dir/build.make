# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.21

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/local/bin/cmake

# The command to remove a file.
RM = /usr/local/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/Desktop/AI_Group/TensorRT/ChaucerG_TRT8_Demo/tensorrt_inference/yolov5-debug

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/Desktop/AI_Group/TensorRT/ChaucerG_TRT8_Demo/tensorrt_inference/yolov5-debug/build

# Include any dependencies generated for this target.
include CMakeFiles/yolov5_trt.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/yolov5_trt.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/yolov5_trt.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/yolov5_trt.dir/flags.make

CMakeFiles/yolov5_trt.dir/main.cpp.o: CMakeFiles/yolov5_trt.dir/flags.make
CMakeFiles/yolov5_trt.dir/main.cpp.o: ../main.cpp
CMakeFiles/yolov5_trt.dir/main.cpp.o: CMakeFiles/yolov5_trt.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Desktop/AI_Group/TensorRT/ChaucerG_TRT8_Demo/tensorrt_inference/yolov5-debug/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/yolov5_trt.dir/main.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/yolov5_trt.dir/main.cpp.o -MF CMakeFiles/yolov5_trt.dir/main.cpp.o.d -o CMakeFiles/yolov5_trt.dir/main.cpp.o -c /home/<USER>/Desktop/AI_Group/TensorRT/ChaucerG_TRT8_Demo/tensorrt_inference/yolov5-debug/main.cpp

CMakeFiles/yolov5_trt.dir/main.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/yolov5_trt.dir/main.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Desktop/AI_Group/TensorRT/ChaucerG_TRT8_Demo/tensorrt_inference/yolov5-debug/main.cpp > CMakeFiles/yolov5_trt.dir/main.cpp.i

CMakeFiles/yolov5_trt.dir/main.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/yolov5_trt.dir/main.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Desktop/AI_Group/TensorRT/ChaucerG_TRT8_Demo/tensorrt_inference/yolov5-debug/main.cpp -o CMakeFiles/yolov5_trt.dir/main.cpp.s

CMakeFiles/yolov5_trt.dir/yolov5.cpp.o: CMakeFiles/yolov5_trt.dir/flags.make
CMakeFiles/yolov5_trt.dir/yolov5.cpp.o: ../yolov5.cpp
CMakeFiles/yolov5_trt.dir/yolov5.cpp.o: CMakeFiles/yolov5_trt.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Desktop/AI_Group/TensorRT/ChaucerG_TRT8_Demo/tensorrt_inference/yolov5-debug/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object CMakeFiles/yolov5_trt.dir/yolov5.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/yolov5_trt.dir/yolov5.cpp.o -MF CMakeFiles/yolov5_trt.dir/yolov5.cpp.o.d -o CMakeFiles/yolov5_trt.dir/yolov5.cpp.o -c /home/<USER>/Desktop/AI_Group/TensorRT/ChaucerG_TRT8_Demo/tensorrt_inference/yolov5-debug/yolov5.cpp

CMakeFiles/yolov5_trt.dir/yolov5.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/yolov5_trt.dir/yolov5.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Desktop/AI_Group/TensorRT/ChaucerG_TRT8_Demo/tensorrt_inference/yolov5-debug/yolov5.cpp > CMakeFiles/yolov5_trt.dir/yolov5.cpp.i

CMakeFiles/yolov5_trt.dir/yolov5.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/yolov5_trt.dir/yolov5.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Desktop/AI_Group/TensorRT/ChaucerG_TRT8_Demo/tensorrt_inference/yolov5-debug/yolov5.cpp -o CMakeFiles/yolov5_trt.dir/yolov5.cpp.s

# Object files for target yolov5_trt
yolov5_trt_OBJECTS = \
"CMakeFiles/yolov5_trt.dir/main.cpp.o" \
"CMakeFiles/yolov5_trt.dir/yolov5.cpp.o"

# External object files for target yolov5_trt
yolov5_trt_EXTERNAL_OBJECTS =

yolov5_trt: CMakeFiles/yolov5_trt.dir/main.cpp.o
yolov5_trt: CMakeFiles/yolov5_trt.dir/yolov5.cpp.o
yolov5_trt: CMakeFiles/yolov5_trt.dir/build.make
yolov5_trt: /usr/local/lib/libopencv_gapi.so.4.5.4
yolov5_trt: /usr/local/lib/libopencv_stitching.so.4.5.4
yolov5_trt: /usr/local/lib/libopencv_aruco.so.4.5.4
yolov5_trt: /usr/local/lib/libopencv_bgsegm.so.4.5.4
yolov5_trt: /usr/local/lib/libopencv_bioinspired.so.4.5.4
yolov5_trt: /usr/local/lib/libopencv_ccalib.so.4.5.4
yolov5_trt: /usr/local/lib/libopencv_cudabgsegm.so.4.5.4
yolov5_trt: /usr/local/lib/libopencv_cudafeatures2d.so.4.5.4
yolov5_trt: /usr/local/lib/libopencv_cudastereo.so.4.5.4
yolov5_trt: /usr/local/lib/libopencv_freetype.so.4.5.4
yolov5_trt: /usr/local/lib/libopencv_fuzzy.so.4.5.4
yolov5_trt: /usr/local/lib/libopencv_hfs.so.4.5.4
yolov5_trt: /usr/local/lib/libopencv_img_hash.so.4.5.4
yolov5_trt: /usr/local/lib/libopencv_intensity_transform.so.4.5.4
yolov5_trt: /usr/local/lib/libopencv_line_descriptor.so.4.5.4
yolov5_trt: /usr/local/lib/libopencv_quality.so.4.5.4
yolov5_trt: /usr/local/lib/libopencv_rapid.so.4.5.4
yolov5_trt: /usr/local/lib/libopencv_reg.so.4.5.4
yolov5_trt: /usr/local/lib/libopencv_rgbd.so.4.5.4
yolov5_trt: /usr/local/lib/libopencv_saliency.so.4.5.4
yolov5_trt: /usr/local/lib/libopencv_shape.so.4.5.4
yolov5_trt: /usr/local/lib/libopencv_stereo.so.4.5.4
yolov5_trt: /usr/local/lib/libopencv_structured_light.so.4.5.4
yolov5_trt: /usr/local/lib/libopencv_superres.so.4.5.4
yolov5_trt: /usr/local/lib/libopencv_surface_matching.so.4.5.4
yolov5_trt: /usr/local/lib/libopencv_tracking.so.4.5.4
yolov5_trt: /usr/local/lib/libopencv_videostab.so.4.5.4
yolov5_trt: /usr/local/lib/libopencv_xphoto.so.4.5.4
yolov5_trt: /usr/local/cuda-11.3/lib64/libcudart_static.a
yolov5_trt: /usr/lib/x86_64-linux-gnu/librt.so
yolov5_trt: /home/<USER>/TensorRT-*******/lib/libnvinfer.so
yolov5_trt: /home/<USER>/TensorRT-*******/lib/libnvonnxparser.so
yolov5_trt: /usr/local/lib/libopencv_highgui.so.4.5.4
yolov5_trt: /usr/local/lib/libopencv_datasets.so.4.5.4
yolov5_trt: /usr/local/lib/libopencv_ml.so.4.5.4
yolov5_trt: /usr/local/lib/libopencv_plot.so.4.5.4
yolov5_trt: /usr/local/lib/libopencv_phase_unwrapping.so.4.5.4
yolov5_trt: /usr/local/lib/libopencv_cudacodec.so.4.5.4
yolov5_trt: /usr/local/lib/libopencv_videoio.so.4.5.4
yolov5_trt: /usr/local/lib/libopencv_cudaoptflow.so.4.5.4
yolov5_trt: /usr/local/lib/libopencv_cudalegacy.so.4.5.4
yolov5_trt: /usr/local/lib/libopencv_cudawarping.so.4.5.4
yolov5_trt: /usr/local/lib/libopencv_optflow.so.4.5.4
yolov5_trt: /usr/local/lib/libopencv_ximgproc.so.4.5.4
yolov5_trt: /usr/local/lib/libopencv_imgcodecs.so.4.5.4
yolov5_trt: /usr/local/lib/libopencv_video.so.4.5.4
yolov5_trt: /usr/local/lib/libopencv_calib3d.so.4.5.4
yolov5_trt: /usr/local/lib/libopencv_features2d.so.4.5.4
yolov5_trt: /usr/local/lib/libopencv_flann.so.4.5.4
yolov5_trt: /usr/local/lib/libopencv_photo.so.4.5.4
yolov5_trt: /usr/local/lib/libopencv_cudaimgproc.so.4.5.4
yolov5_trt: /usr/local/lib/libopencv_cudafilters.so.4.5.4
yolov5_trt: /usr/local/lib/libopencv_imgproc.so.4.5.4
yolov5_trt: /usr/local/lib/libopencv_cudaarithm.so.4.5.4
yolov5_trt: /usr/local/lib/libopencv_core.so.4.5.4
yolov5_trt: /usr/local/lib/libopencv_cudev.so.4.5.4
yolov5_trt: CMakeFiles/yolov5_trt.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/home/<USER>/Desktop/AI_Group/TensorRT/ChaucerG_TRT8_Demo/tensorrt_inference/yolov5-debug/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Linking CXX executable yolov5_trt"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/yolov5_trt.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/yolov5_trt.dir/build: yolov5_trt
.PHONY : CMakeFiles/yolov5_trt.dir/build

CMakeFiles/yolov5_trt.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/yolov5_trt.dir/cmake_clean.cmake
.PHONY : CMakeFiles/yolov5_trt.dir/clean

CMakeFiles/yolov5_trt.dir/depend:
	cd /home/<USER>/Desktop/AI_Group/TensorRT/ChaucerG_TRT8_Demo/tensorrt_inference/yolov5-debug/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/Desktop/AI_Group/TensorRT/ChaucerG_TRT8_Demo/tensorrt_inference/yolov5-debug /home/<USER>/Desktop/AI_Group/TensorRT/ChaucerG_TRT8_Demo/tensorrt_inference/yolov5-debug /home/<USER>/Desktop/AI_Group/TensorRT/ChaucerG_TRT8_Demo/tensorrt_inference/yolov5-debug/build /home/<USER>/Desktop/AI_Group/TensorRT/ChaucerG_TRT8_Demo/tensorrt_inference/yolov5-debug/build /home/<USER>/Desktop/AI_Group/TensorRT/ChaucerG_TRT8_Demo/tensorrt_inference/yolov5-debug/build/CMakeFiles/yolov5_trt.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : CMakeFiles/yolov5_trt.dir/depend

