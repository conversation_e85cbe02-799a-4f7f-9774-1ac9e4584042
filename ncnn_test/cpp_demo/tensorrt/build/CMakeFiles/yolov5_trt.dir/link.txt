/usr/bin/c++ CMakeFiles/yolov5_trt.dir/main.cpp.o CMakeFiles/yolov5_trt.dir/yolov5.cpp.o -o yolov5_trt   -L/usr/local/cuda-11.3/lib64  -L/home/<USER>/Desktop/AI_Group/TensorRT/ChaucerG_TRT8_Demo/tensorrt_inference/yolov5-debug/./includes/yaml-cpp/libs  -Wl,-rpath,/usr/local/cuda-11.3/lib64:/home/<USER>/Desktop/AI_Group/TensorRT/ChaucerG_TRT8_Demo/tensorrt_inference/yolov5-debug/./includes/yaml-cpp/libs:/usr/local/lib:/home/<USER>/TensorRT-*******/lib /usr/local/lib/libopencv_gapi.so.4.5.4 /usr/local/lib/libopencv_stitching.so.4.5.4 /usr/local/lib/libopencv_aruco.so.4.5.4 /usr/local/lib/libopencv_bgsegm.so.4.5.4 /usr/local/lib/libopencv_bioinspired.so.4.5.4 /usr/local/lib/libopencv_ccalib.so.4.5.4 /usr/local/lib/libopencv_cudabgsegm.so.4.5.4 /usr/local/lib/libopencv_cudafeatures2d.so.4.5.4 /usr/local/lib/libopencv_cudastereo.so.4.5.4 /usr/local/lib/libopencv_freetype.so.4.5.4 /usr/local/lib/libopencv_fuzzy.so.4.5.4 /usr/local/lib/libopencv_hfs.so.4.5.4 /usr/local/lib/libopencv_img_hash.so.4.5.4 /usr/local/lib/libopencv_intensity_transform.so.4.5.4 /usr/local/lib/libopencv_line_descriptor.so.4.5.4 /usr/local/lib/libopencv_quality.so.4.5.4 /usr/local/lib/libopencv_rapid.so.4.5.4 /usr/local/lib/libopencv_reg.so.4.5.4 /usr/local/lib/libopencv_rgbd.so.4.5.4 /usr/local/lib/libopencv_saliency.so.4.5.4 /usr/local/lib/libopencv_shape.so.4.5.4 /usr/local/lib/libopencv_stereo.so.4.5.4 /usr/local/lib/libopencv_structured_light.so.4.5.4 /usr/local/lib/libopencv_superres.so.4.5.4 /usr/local/lib/libopencv_surface_matching.so.4.5.4 /usr/local/lib/libopencv_tracking.so.4.5.4 /usr/local/lib/libopencv_videostab.so.4.5.4 /usr/local/lib/libopencv_xphoto.so.4.5.4 /usr/local/cuda-11.3/lib64/libcudart_static.a -ldl /usr/lib/x86_64-linux-gnu/librt.so /home/<USER>/TensorRT-*******/lib/libnvinfer.so /home/<USER>/TensorRT-*******/lib/libnvonnxparser.so -lyaml-cpp /usr/local/lib/libopencv_highgui.so.4.5.4 /usr/local/lib/libopencv_datasets.so.4.5.4 /usr/local/lib/libopencv_ml.so.4.5.4 /usr/local/lib/libopencv_plot.so.4.5.4 /usr/local/lib/libopencv_phase_unwrapping.so.4.5.4 /usr/local/lib/libopencv_cudacodec.so.4.5.4 /usr/local/lib/libopencv_videoio.so.4.5.4 /usr/local/lib/libopencv_cudaoptflow.so.4.5.4 /usr/local/lib/libopencv_cudalegacy.so.4.5.4 /usr/local/lib/libopencv_cudawarping.so.4.5.4 /usr/local/lib/libopencv_optflow.so.4.5.4 /usr/local/lib/libopencv_ximgproc.so.4.5.4 /usr/local/lib/libopencv_imgcodecs.so.4.5.4 /usr/local/lib/libopencv_video.so.4.5.4 /usr/local/lib/libopencv_calib3d.so.4.5.4 /usr/local/lib/libopencv_features2d.so.4.5.4 /usr/local/lib/libopencv_flann.so.4.5.4 /usr/local/lib/libopencv_photo.so.4.5.4 /usr/local/lib/libopencv_cudaimgproc.so.4.5.4 /usr/local/lib/libopencv_cudafilters.so.4.5.4 /usr/local/lib/libopencv_imgproc.so.4.5.4 /usr/local/lib/libopencv_cudaarithm.so.4.5.4 /usr/local/lib/libopencv_core.so.4.5.4 /usr/local/lib/libopencv_cudev.so.4.5.4 -lpthread 
