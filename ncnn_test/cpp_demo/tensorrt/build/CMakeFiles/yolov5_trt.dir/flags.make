# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.21

# compile CXX with /usr/bin/c++
CXX_DEFINES = 

CXX_INCLUDES = -I/usr/local/cuda-11.3/include -I/home/<USER>/TensorRT-8.0.1.6/include -I/home/<USER>/Desktop/AI_Group/TensorRT/ChaucerG_TRT8_Demo/tensorrt_inference/yolov5-debug/./includes/common -I/home/<USER>/Desktop/AI_Group/TensorRT/ChaucerG_TRT8_Demo/tensorrt_inference/yolov5-debug/./includes/yaml-cpp/include -isystem /usr/local/include/opencv4

CXX_FLAGS = -std=gnu++14

