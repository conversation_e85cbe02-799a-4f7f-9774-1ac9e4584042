/*
 * Copyright 2006 The Android Open Source Project
 *
 * Use of this source code is governed by a BSD-style license that can be
 * found in the LICENSE file.
 */

/* Generated by tools/bookmaker from include/core/Matrix.h and docs/SkMatrix_Reference.bmh
   on 2018-07-13 08:15:11. Additional documentation and examples can be found at:
   https://skia.org/user/api/SkMatrix_Reference

   You may edit either file directly. Structural changes to public interfaces require
   editing both files. After editing docs/SkMatrix_Reference.bmh, run:
       bookmaker -b docs -i include/core/Matrix.h -p
   to create an updated version of this file.
 */


//
//  Modified by jiangxiaotang on 2018/09/19.
//  Copyright © 2018, Alibaba Group Holding Limited
//

#ifndef MNN_Matrix_DEFINED
#define MNN_Matrix_DEFINED

#include <string.h>
#include <cstdint>
#include <MNN/Rect.h>

namespace MNN {
namespace CV {

/** \class Matrix
    Matrix holds a 3x3 matrix for transforming coordinates. This allows mapping
    Point and vectors with translation, scaling, skewing, rotation, and
    perspective.

    Matrix elements are in row major order. Matrix does not have a constructor,
    so it must be explicitly initialized. setIdentity() initializes Matrix
    so it has no effect. setTranslate(), setScale(), setSkew(), setRotate(), set9 and setAll()
    initializes all Matrix elements with the corresponding mapping.

    Matrix includes a hidden variable that classifies the type of matrix to
    improve performance. Matrix is not thread safe unless getType() is called first.
*/

class MNN_PUBLIC Matrix {
public:
    Matrix() {
        setIdentity();
    }

    /** Sets Matrix to scale by (sx, sy). Returned matrix is:

            | sx  0  0 |
            |  0 sy  0 |
            |  0  0  1 |

        @param sx  horizontal scale factor
        @param sy  vertical scale factor
        @return    Matrix with scale
    */
    static Matrix MakeScale(float sx, float sy) {
        Matrix m;
        m.setScale(sx, sy);
        return m;
    }

    /** Sets Matrix to scale by (scale, scale). Returned matrix is:

            | scale   0   0 |
            |   0   scale 0 |
            |   0     0   1 |

        @param scale  horizontal and vertical scale factor
        @return       Matrix with scale
    */
    static Matrix MakeScale(float scale) {
        Matrix m;
        m.setScale(scale, scale);
        return m;
    }

    /** Sets Matrix to translate by (dx, dy). Returned matrix is:

            | 1 0 dx |
            | 0 1 dy |
            | 0 0  1 |

        @param dx  horizontal translation
        @param dy  vertical translation
        @return    Matrix with translation
    */
    static Matrix MakeTrans(float dx, float dy) {
        Matrix m;
        m.setTranslate(dx, dy);
        return m;
    }

    /** Sets Matrix to:

            | scaleX  skewX transX |
            |  skewY scaleY transY |
            |  pers0  pers1  pers2 |

        @param scaleX  horizontal scale factor
        @param skewX   horizontal skew factor
        @param transX  horizontal translation
        @param skewY   vertical skew factor
        @param scaleY  vertical scale factor
        @param transY  vertical translation
        @param pers0   input x-axis perspective factor
        @param pers1   input y-axis perspective factor
        @param pers2   perspective scale factor
        @return        Matrix constructed from parameters
    */
    static Matrix MakeAll(float scaleX, float skewX, float transX, float skewY, float scaleY, float transY, float pers0,
                          float pers1, float pers2) {
        Matrix m;
        m.setAll(scaleX, skewX, transX, skewY, scaleY, transY, pers0, pers1, pers2);
        return m;
    }

    /** \enum Matrix::TypeMask
        Enum of bit fields for mask returned by getType().
        Used to identify the complexity of Matrix, to optimize performance.
    */
    enum TypeMask {
        kIdentity_Mask    = 0,    //!< identity Matrix; all bits clear
        kTranslate_Mask   = 0x01, //!< translation Matrix
        kScale_Mask       = 0x02, //!< scale Matrix
        kAffine_Mask      = 0x04, //!< skew or rotate Matrix
        kPerspective_Mask = 0x08, //!< perspective Matrix
    };

    /** Returns a bit field describing the transformations the matrix may
        perform. The bit field is computed conservatively, so it may include
        false positives. For example, when kPerspective_Mask is set, all
        other bits are set.

        @return  kIdentity_Mask, or combinations of: kTranslate_Mask, kScale_Mask,
                 kAffine_Mask, kPerspective_Mask
    */
    TypeMask getType() const {
        if (fTypeMask & kUnknown_Mask) {
            fTypeMask = this->computeTypeMask();
        }
        // only return the public masks
        return (TypeMask)(fTypeMask & 0xF);
    }

    /** Returns true if Matrix is identity.  Identity matrix is:

            | 1 0 0 |
            | 0 1 0 |
            | 0 0 1 |

        @return  true if Matrix has no effect
    */
    bool isIdentity() const {
        return this->getType() == 0;
    }

    /** Returns true if Matrix at most scales and translates. Matrix may be identity,
        contain only scale elements, only translate elements, or both. Matrix form is:

            | scale-x    0    translate-x |
            |    0    scale-y translate-y |
            |    0       0         1      |

        @return  true if Matrix is identity; or scales, translates, or both
    */
    bool isScaleTranslate() const {
        return !(this->getType() & ~(kScale_Mask | kTranslate_Mask));
    }

    /** Returns true if Matrix is identity, or translates. Matrix form is:

            | 1 0 translate-x |
            | 0 1 translate-y |
            | 0 0      1      |

        @return  true if Matrix is identity, or translates
    */
    bool isTranslate() const {
        return !(this->getType() & ~(kTranslate_Mask));
    }

    /** Returns true Matrix maps Rect to another Rect. If true, Matrix is identity,
        or scales, or rotates a multiple of 90 degrees, or mirrors on axes. In all
        cases, Matrix may also have translation. Matrix form is either:

            | scale-x    0    translate-x |
            |    0    scale-y translate-y |
            |    0       0         1      |

        or

            |    0     rotate-x translate-x |
            | rotate-y    0     translate-y |
            |    0        0          1      |

        for non-zero values of scale-x, scale-y, rotate-x, and rotate-y.

        Also called preservesAxisAlignment(); use the one that provides better inline
        documentation.

        @return  true if Matrix maps one Rect into another
    */
    bool rectStaysRect() const {
        if (fTypeMask & kUnknown_Mask) {
            fTypeMask = this->computeTypeMask();
        }
        return (fTypeMask & kRectStaysRect_Mask) != 0;
    }

    /** Returns true Matrix maps Rect to another Rect. If true, Matrix is identity,
        or scales, or rotates a multiple of 90 degrees, or mirrors on axes. In all
        cases, Matrix may also have translation. Matrix form is either:

            | scale-x    0    translate-x |
            |    0    scale-y translate-y |
            |    0       0         1      |

        or

            |    0     rotate-x translate-x |
            | rotate-y    0     translate-y |
            |    0        0          1      |

        for non-zero values of scale-x, scale-y, rotate-x, and rotate-y.

        Also called rectStaysRect(); use the one that provides better inline
        documentation.

        @return  true if Matrix maps one Rect into another
    */
    bool preservesAxisAlignment() const {
        return this->rectStaysRect();
    }

    /** Matrix organizes its values in row order. These members correspond to
        each value in Matrix.
    */
    static constexpr int kMScaleX = 0; //!< horizontal scale factor
    static constexpr int kMSkewX  = 1; //!< horizontal skew factor
    static constexpr int kMTransX = 2; //!< horizontal translation
    static constexpr int kMSkewY  = 3; //!< vertical skew factor
    static constexpr int kMScaleY = 4; //!< vertical scale factor
    static constexpr int kMTransY = 5; //!< vertical translation
    static constexpr int kMPersp0 = 6; //!< input x perspective factor
    static constexpr int kMPersp1 = 7; //!< input y perspective factor
    static constexpr int kMPersp2 = 8; //!< perspective bias

    /** Affine arrays are in column major order to match the matrix used by
        PDF and XPS.
    */
    static constexpr int kAScaleX = 0; //!< horizontal scale factor
    static constexpr int kASkewY  = 1; //!< vertical skew factor
    static constexpr int kASkewX  = 2; //!< horizontal skew factor
    static constexpr int kAScaleY = 3; //!< vertical scale factor
    static constexpr int kATransX = 4; //!< horizontal translation
    static constexpr int kATransY = 5; //!< vertical translation

    /** Returns one matrix value. Asserts if index is out of range and SK_DEBUG is
        defined.

        @param index  one of: kMScaleX, kMSkewX, kMTransX, kMSkewY, kMScaleY, kMTransY,
                      kMPersp0, kMPersp1, kMPersp2
        @return       value corresponding to index
    */
    float operator[](int index) const {
        MNN_ASSERT((unsigned)index < 9);
        return fMat[index];
    }

    /** Returns one matrix value. Asserts if index is out of range and SK_DEBUG is
        defined.

        @param index  one of: kMScaleX, kMSkewX, kMTransX, kMSkewY, kMScaleY, kMTransY,
                      kMPersp0, kMPersp1, kMPersp2
        @return       value corresponding to index
    */
    float get(int index) const {
        MNN_ASSERT((unsigned)index < 9);
        return fMat[index];
    }

    /** Returns scale factor multiplied by x-axis input, contributing to x-axis output.
        With mapPoints(), scales Point along the x-axis.

        @return  horizontal scale factor
    */
    float getScaleX() const {
        return fMat[kMScaleX];
    }

    /** Returns scale factor multiplied by y-axis input, contributing to y-axis output.
        With mapPoints(), scales Point along the y-axis.

        @return  vertical scale factor
    */
    float getScaleY() const {
        return fMat[kMScaleY];
    }

    /** Returns scale factor multiplied by x-axis input, contributing to y-axis output.
        With mapPoints(), skews Point along the y-axis.
        Skewing both axes can rotate Point.

        @return  vertical skew factor
    */
    float getSkewY() const {
        return fMat[kMSkewY];
    }

    /** Returns scale factor multiplied by y-axis input, contributing to x-axis output.
        With mapPoints(), skews Point along the x-axis.
        Skewing both axes can rotate Point.

        @return  horizontal scale factor
    */
    float getSkewX() const {
        return fMat[kMSkewX];
    }

    /** Returns translation contributing to x-axis output.
        With mapPoints(), moves Point along the x-axis.

        @return  horizontal translation factor
    */
    float getTranslateX() const {
        return fMat[kMTransX];
    }

    /** Returns translation contributing to y-axis output.
        With mapPoints(), moves Point along the y-axis.

        @return  vertical translation factor
    */
    float getTranslateY() const {
        return fMat[kMTransY];
    }

    /** Returns factor scaling input x-axis relative to input y-axis.

        @return  input x-axis perspective factor
    */
    float getPerspX() const {
        return fMat[kMPersp0];
    }

    /** Returns factor scaling input y-axis relative to input x-axis.

        @return  input y-axis perspective factor
    */
    float getPerspY() const {
        return fMat[kMPersp1];
    }

    /** Returns writable Matrix value. Asserts if index is out of range and SK_DEBUG is
        defined. Clears internal cache anticipating that caller will change Matrix value.

        Next call to read Matrix state may recompute cache; subsequent writes to Matrix
        value must be followed by dirtyMatrixTypeCache().

        @param index  one of: kMScaleX, kMSkewX, kMTransX, kMSkewY, kMScaleY, kMTransY,
                      kMPersp0, kMPersp1, kMPersp2
        @return       writable value corresponding to index
    */
    float& operator[](int index) {
        MNN_ASSERT((unsigned)index < 9);
        this->setTypeMask(kUnknown_Mask);
        return fMat[index];
    }

    /** Sets Matrix value. Asserts if index is out of range and SK_DEBUG is
        defined. Safer than operator[]; internal cache is always maintained.

        @param index  one of: kMScaleX, kMSkewX, kMTransX, kMSkewY, kMScaleY, kMTransY,
                      kMPersp0, kMPersp1, kMPersp2
        @param value  scalar to store in Matrix
    */
    void set(int index, float value) {
        MNN_ASSERT((unsigned)index < 9);
        fMat[index] = value;
        this->setTypeMask(kUnknown_Mask);
    }

    /** Sets horizontal scale factor.

        @param v  horizontal scale factor to store
    */
    void setScaleX(float v) {
        this->set(kMScaleX, v);
    }

    /** Sets vertical scale factor.

        @param v  vertical scale factor to store
    */
    void setScaleY(float v) {
        this->set(kMScaleY, v);
    }

    /** Sets vertical skew factor.

        @param v  vertical skew factor to store
    */
    void setSkewY(float v) {
        this->set(kMSkewY, v);
    }

    /** Sets horizontal skew factor.

        @param v  horizontal skew factor to store
    */
    void setSkewX(float v) {
        this->set(kMSkewX, v);
    }

    /** Sets horizontal translation.

        @param v  horizontal translation to store
    */
    void setTranslateX(float v) {
        this->set(kMTransX, v);
    }

    /** Sets vertical translation.

        @param v  vertical translation to store
    */
    void setTranslateY(float v) {
        this->set(kMTransY, v);
    }

    /** Sets input x-axis perspective factor, which causes mapXY() to vary input x-axis values
        inversely proportional to input y-axis values.

        @param v  perspective factor
    */
    void setPerspX(float v) {
        this->set(kMPersp0, v);
    }

    /** Sets input y-axis perspective factor, which causes mapXY() to vary input y-axis values
        inversely proportional to input x-axis values.

        @param v  perspective factor
    */
    void setPerspY(float v) {
        this->set(kMPersp1, v);
    }

    /** Sets all values from parameters. Sets matrix to:

            | scaleX  skewX transX |
            |  skewY scaleY transY |
            | persp0 persp1 persp2 |

        @param scaleX  horizontal scale factor to store
        @param skewX   horizontal skew factor to store
        @param transX  horizontal translation to store
        @param skewY   vertical skew factor to store
        @param scaleY  vertical scale factor to store
        @param transY  vertical translation to store
        @param persp0  input x-axis values perspective factor to store
        @param persp1  input y-axis values perspective factor to store
        @param persp2  perspective scale factor to store
    */
    void setAll(float scaleX, float skewX, float transX, float skewY, float scaleY, float transY, float persp0,
                float persp1, float persp2) {
        fMat[kMScaleX] = scaleX;
        fMat[kMSkewX]  = skewX;
        fMat[kMTransX] = transX;
        fMat[kMSkewY]  = skewY;
        fMat[kMScaleY] = scaleY;
        fMat[kMTransY] = transY;
        fMat[kMPersp0] = persp0;
        fMat[kMPersp1] = persp1;
        fMat[kMPersp2] = persp2;
        this->setTypeMask(kUnknown_Mask);
    }

    /** Copies nine scalar values contained by Matrix into buffer, in member value
        ascending order: kMScaleX, kMSkewX, kMTransX, kMSkewY, kMScaleY, kMTransY,
        kMPersp0, kMPersp1, kMPersp2.

        @param buffer  storage for nine scalar values
    */
    void get9(float buffer[9]) const {
        memcpy(buffer, fMat, 9 * sizeof(float));
    }

    /** Sets Matrix to nine scalar values in buffer, in member value ascending order:
        kMScaleX, kMSkewX, kMTransX, kMSkewY, kMScaleY, kMTransY, kMPersp0, kMPersp1,
        kMPersp2.

        Sets matrix to:

            | buffer[0] buffer[1] buffer[2] |
            | buffer[3] buffer[4] buffer[5] |
            | buffer[6] buffer[7] buffer[8] |

        In the future, set9 followed by get9 may not return the same values. Since Matrix
        maps non-homogeneous coordinates, scaling all nine values produces an equivalent
        transformation, possibly improving precision.

        @param buffer  nine scalar values
    */
    void set9(const float buffer[9]);

    /** Sets Matrix to identity; which has no effect on mapped Point. Sets Matrix to:

            | 1 0 0 |
            | 0 1 0 |
            | 0 0 1 |

        Also called setIdentity(); use the one that provides better inline
        documentation.
    */
    void reset();

    /** Sets Matrix to identity; which has no effect on mapped Point. Sets Matrix to:

            | 1 0 0 |
            | 0 1 0 |
            | 0 0 1 |

        Also called reset(); use the one that provides better inline
        documentation.
    */
    void setIdentity() {
        this->reset();
    }

    /** Sets Matrix to translate by (dx, dy).

        @param dx  horizontal translation
        @param dy  vertical translation
    */
    void setTranslate(float dx, float dy);

    /** Sets Matrix to scale by sx and sy, about a pivot point at (px, py).
        The pivot point is unchanged when mapped with Matrix.

        @param sx  horizontal scale factor
        @param sy  vertical scale factor
        @param px  pivot x
        @param py  pivot y
    */
    void setScale(float sx, float sy, float px, float py);

    /** Sets Matrix to scale by sx and sy about at pivot point at (0, 0).

        @param sx  horizontal scale factor
        @param sy  vertical scale factor
    */
    void setScale(float sx, float sy);

    /** Sets Matrix to rotate by degrees about a pivot point at (px, py).
        The pivot point is unchanged when mapped with Matrix.

        Positive degrees rotates clockwise.

        @param degrees  angle of axes relative to upright axes
        @param px       pivot x
        @param py       pivot y
    */
    void setRotate(float degrees, float px, float py);

    /** Sets Matrix to rotate by degrees about a pivot point at (0, 0).
        Positive degrees rotates clockwise.

        @param degrees  angle of axes relative to upright axes
    */
    void setRotate(float degrees);

    /** Sets Matrix to rotate by sinValue and cosValue, about a pivot point at (px, py).
        The pivot point is unchanged when mapped with Matrix.

        Vector (sinValue, cosValue) describes the angle of rotation relative to (0, 1).
        Vector length specifies scale.

        @param sinValue  rotation vector x-axis component
        @param cosValue  rotation vector y-axis component
        @param px        pivot x-axis
        @param py        pivot y-axis
    */
    void setSinCos(float sinValue, float cosValue, float px, float py);

    /** Sets Matrix to rotate by sinValue and cosValue, about a pivot point at (0, 0).

        Vector (sinValue, cosValue) describes the angle of rotation relative to (0, 1).
        Vector length specifies scale.

        @param sinValue  rotation vector x-axis component
        @param cosValue  rotation vector y-axis component
    */
    void setSinCos(float sinValue, float cosValue);

    /** Sets Matrix to skew by kx and ky, about a pivot point at (px, py).
        The pivot point is unchanged when mapped with Matrix.

        @param kx  horizontal skew factor
        @param ky  vertical skew factor
        @param px  pivot x
        @param py  pivot y
    */
    void setSkew(float kx, float ky, float px, float py);

    /** Sets Matrix to skew by kx and ky, about a pivot point at (0, 0).

        @param kx  horizontal skew factor
        @param ky  vertical skew factor
    */
    void setSkew(float kx, float ky);

    /** Sets Matrix to Matrix a multiplied by Matrix b. Either a or b may be this.

        Given:

                | A B C |      | J K L |
            a = | D E F |, b = | M N O |
                | G H I |      | P Q R |

        sets Matrix to:

                    | A B C |   | J K L |   | AJ+BM+CP AK+BN+CQ AL+BO+CR |
            a * b = | D E F | * | M N O | = | DJ+EM+FP DK+EN+FQ DL+EO+FR |
                    | G H I |   | P Q R |   | GJ+HM+IP GK+HN+IQ GL+HO+IR |

        @param a  Matrix on left side of multiply expression
        @param b  Matrix on right side of multiply expression
    */
    void setConcat(const Matrix& a, const Matrix& b);

    /** Sets Matrix to Matrix multiplied by Matrix constructed from translation (dx, dy).
        This can be thought of as moving the point to be mapped before applying Matrix.

        Given:

                     | A B C |               | 1 0 dx |
            Matrix = | D E F |,  T(dx, dy) = | 0 1 dy |
                     | G H I |               | 0 0  1 |

        sets Matrix to:

                                 | A B C | | 1 0 dx |   | A B A*dx+B*dy+C |
            Matrix * T(dx, dy) = | D E F | | 0 1 dy | = | D E D*dx+E*dy+F |
                                 | G H I | | 0 0  1 |   | G H G*dx+H*dy+I |

        @param dx  x-axis translation before applying Matrix
        @param dy  y-axis translation before applying Matrix
    */
    void preTranslate(float dx, float dy);

    /** Sets Matrix to Matrix multiplied by Matrix constructed from scaling by (sx, sy)
        about pivot point (px, py).
        This can be thought of as scaling about a pivot point before applying Matrix.

        Given:

                     | A B C |                       | sx  0 dx |
            Matrix = | D E F |,  S(sx, sy, px, py) = |  0 sy dy |
                     | G H I |                       |  0  0  1 |

        where

            dx = px - sx * px
            dy = py - sy * py

        sets Matrix to:

                                         | A B C | | sx  0 dx |   | A*sx B*sy A*dx+B*dy+C |
            Matrix * S(sx, sy, px, py) = | D E F | |  0 sy dy | = | D*sx E*sy D*dx+E*dy+F |
                                         | G H I | |  0  0  1 |   | G*sx H*sy G*dx+H*dy+I |

        @param sx  horizontal scale factor
        @param sy  vertical scale factor
        @param px  pivot x
        @param py  pivot y
    */
    void preScale(float sx, float sy, float px, float py);

    /** Sets Matrix to Matrix multiplied by Matrix constructed from scaling by (sx, sy)
        about pivot point (0, 0).
        This can be thought of as scaling about the origin before applying Matrix.

        Given:

                     | A B C |               | sx  0  0 |
            Matrix = | D E F |,  S(sx, sy) = |  0 sy  0 |
                     | G H I |               |  0  0  1 |

        sets Matrix to:

                                 | A B C | | sx  0  0 |   | A*sx B*sy C |
            Matrix * S(sx, sy) = | D E F | |  0 sy  0 | = | D*sx E*sy F |
                                 | G H I | |  0  0  1 |   | G*sx H*sy I |

        @param sx  horizontal scale factor
        @param sy  vertical scale factor
    */
    void preScale(float sx, float sy);

    /** Sets Matrix to Matrix multiplied by Matrix constructed from rotating by degrees
        about pivot point (px, py).
        This can be thought of as rotating about a pivot point before applying Matrix.

        Positive degrees rotates clockwise.

        Given:

                     | A B C |                        | c -s dx |
            Matrix = | D E F |,  R(degrees, px, py) = | s  c dy |
                     | G H I |                        | 0  0  1 |

        where

            c  = cos(degrees)
            s  = sin(degrees)
            dx =  s * py + (1 - c) * px
            dy = -s * px + (1 - c) * py

        sets Matrix to:

                                          | A B C | | c -s dx |   | Ac+Bs -As+Bc A*dx+B*dy+C |
            Matrix * R(degrees, px, py) = | D E F | | s  c dy | = | Dc+Es -Ds+Ec D*dx+E*dy+F |
                                          | G H I | | 0  0  1 |   | Gc+Hs -Gs+Hc G*dx+H*dy+I |

        @param degrees  angle of axes relative to upright axes
        @param px       pivot x
        @param py       pivot y
    */
    void preRotate(float degrees, float px, float py);

    /** Sets Matrix to Matrix multiplied by Matrix constructed from rotating by degrees
        about pivot point (0, 0).
        This can be thought of as rotating about the origin before applying Matrix.

        Positive degrees rotates clockwise.

        Given:

                     | A B C |                        | c -s 0 |
            Matrix = | D E F |,  R(degrees, px, py) = | s  c 0 |
                     | G H I |                        | 0  0 1 |

        where

            c  = cos(degrees)
            s  = sin(degrees)

        sets Matrix to:

                                          | A B C | | c -s 0 |   | Ac+Bs -As+Bc C |
            Matrix * R(degrees, px, py) = | D E F | | s  c 0 | = | Dc+Es -Ds+Ec F |
                                          | G H I | | 0  0 1 |   | Gc+Hs -Gs+Hc I |

        @param degrees  angle of axes relative to upright axes
    */
    void preRotate(float degrees);

    /** Sets Matrix to Matrix multiplied by Matrix constructed from skewing by (kx, ky)
        about pivot point (px, py).
        This can be thought of as skewing about a pivot point before applying Matrix.

        Given:

                     | A B C |                       |  1 kx dx |
            Matrix = | D E F |,  K(kx, ky, px, py) = | ky  1 dy |
                     | G H I |                       |  0  0  1 |

        where

            dx = -kx * py
            dy = -ky * px

        sets Matrix to:

                                         | A B C | |  1 kx dx |   | A+B*ky A*kx+B A*dx+B*dy+C |
            Matrix * K(kx, ky, px, py) = | D E F | | ky  1 dy | = | D+E*ky D*kx+E D*dx+E*dy+F |
                                         | G H I | |  0  0  1 |   | G+H*ky G*kx+H G*dx+H*dy+I |

        @param kx  horizontal skew factor
        @param ky  vertical skew factor
        @param px  pivot x
        @param py  pivot y
    */
    void preSkew(float kx, float ky, float px, float py);

    /** Sets Matrix to Matrix multiplied by Matrix constructed from skewing by (kx, ky)
        about pivot point (0, 0).
        This can be thought of as skewing about the origin before applying Matrix.

        Given:

                     | A B C |               |  1 kx 0 |
            Matrix = | D E F |,  K(kx, ky) = | ky  1 0 |
                     | G H I |               |  0  0 1 |

        sets Matrix to:

                                 | A B C | |  1 kx 0 |   | A+B*ky A*kx+B C |
            Matrix * K(kx, ky) = | D E F | | ky  1 0 | = | D+E*ky D*kx+E F |
                                 | G H I | |  0  0 1 |   | G+H*ky G*kx+H I |

        @param kx  horizontal skew factor
        @param ky  vertical skew factor
    */
    void preSkew(float kx, float ky);

    /** Sets Matrix to Matrix multiplied by Matrix other.
        This can be thought of mapping by other before applying Matrix.

        Given:

                     | A B C |          | J K L |
            Matrix = | D E F |, other = | M N O |
                     | G H I |          | P Q R |

        sets Matrix to:

                             | A B C |   | J K L |   | AJ+BM+CP AK+BN+CQ AL+BO+CR |
            Matrix * other = | D E F | * | M N O | = | DJ+EM+FP DK+EN+FQ DL+EO+FR |
                             | G H I |   | P Q R |   | GJ+HM+IP GK+HN+IQ GL+HO+IR |

        @param other  Matrix on right side of multiply expression
    */
    void preConcat(const Matrix& other);

    /** Sets Matrix to Matrix constructed from translation (dx, dy) multiplied by Matrix.
        This can be thought of as moving the point to be mapped after applying Matrix.

        Given:

                     | J K L |               | 1 0 dx |
            Matrix = | M N O |,  T(dx, dy) = | 0 1 dy |
                     | P Q R |               | 0 0  1 |

        sets Matrix to:

                                 | 1 0 dx | | J K L |   | J+dx*P K+dx*Q L+dx*R |
            T(dx, dy) * Matrix = | 0 1 dy | | M N O | = | M+dy*P N+dy*Q O+dy*R |
                                 | 0 0  1 | | P Q R |   |      P      Q      R |

        @param dx  x-axis translation after applying Matrix
        @param dy  y-axis translation after applying Matrix
    */
    void postTranslate(float dx, float dy);

    /** Sets Matrix to Matrix constructed from scaling by (sx, sy) about pivot point
        (px, py), multiplied by Matrix.
        This can be thought of as scaling about a pivot point after applying Matrix.

        Given:

                     | J K L |                       | sx  0 dx |
            Matrix = | M N O |,  S(sx, sy, px, py) = |  0 sy dy |
                     | P Q R |                       |  0  0  1 |

        where

            dx = px - sx * px
            dy = py - sy * py

        sets Matrix to:

                                         | sx  0 dx | | J K L |   | sx*J+dx*P sx*K+dx*Q sx*L+dx+R |
            S(sx, sy, px, py) * Matrix = |  0 sy dy | | M N O | = | sy*M+dy*P sy*N+dy*Q sy*O+dy*R |
                                         |  0  0  1 | | P Q R |   |         P         Q         R |

        @param sx  horizontal scale factor
        @param sy  vertical scale factor
        @param px  pivot x
        @param py  pivot y
    */
    void postScale(float sx, float sy, float px, float py);

    /** Sets Matrix to Matrix constructed from scaling by (sx, sy) about pivot point
        (0, 0), multiplied by Matrix.
        This can be thought of as scaling about the origin after applying Matrix.

        Given:

                     | J K L |               | sx  0  0 |
            Matrix = | M N O |,  S(sx, sy) = |  0 sy  0 |
                     | P Q R |               |  0  0  1 |

        sets Matrix to:

                                 | sx  0  0 | | J K L |   | sx*J sx*K sx*L |
            S(sx, sy) * Matrix = |  0 sy  0 | | M N O | = | sy*M sy*N sy*O |
                                 |  0  0  1 | | P Q R |   |    P    Q    R |

        @param sx  horizontal scale factor
        @param sy  vertical scale factor
    */
    void postScale(float sx, float sy);

    /** Sets Matrix to Matrix constructed from scaling by (1/divx, 1/divy) about pivot point (px, py), multiplied by
       Matrix.

        Returns false if either divx or divy is zero.

        Given:

                     | J K L |                   | sx  0  0 |
            Matrix = | M N O |,  I(divx, divy) = |  0 sy  0 |
                     | P Q R |                   |  0  0  1 |

        where

            sx = 1 / divx
            sy = 1 / divy

        sets Matrix to:

                                     | sx  0  0 | | J K L |   | sx*J sx*K sx*L |
            I(divx, divy) * Matrix = |  0 sy  0 | | M N O | = | sy*M sy*N sy*O |
                                     |  0  0  1 | | P Q R |   |    P    Q    R |

        @param divx  integer divisor for inverse scale in x
        @param divy  integer divisor for inverse scale in y
        @return      true on successful scale
    */
    bool postIDiv(int divx, int divy);

    /** Sets Matrix to Matrix constructed from rotating by degrees about pivot point
        (px, py), multiplied by Matrix.
        This can be thought of as rotating about a pivot point after applying Matrix.

        Positive degrees rotates clockwise.

        Given:

                     | J K L |                        | c -s dx |
            Matrix = | M N O |,  R(degrees, px, py) = | s  c dy |
                     | P Q R |                        | 0  0  1 |

        where

            c  = cos(degrees)
            s  = sin(degrees)
            dx =  s * py + (1 - c) * px
            dy = -s * px + (1 - c) * py

        sets Matrix to:

                                          |c -s dx| |J K L|   |cJ-sM+dx*P cK-sN+dx*Q cL-sO+dx+R|
            R(degrees, px, py) * Matrix = |s  c dy| |M N O| = |sJ+cM+dy*P sK+cN+dy*Q sL+cO+dy*R|
                                          |0  0  1| |P Q R|   |         P          Q          R|

        @param degrees  angle of axes relative to upright axes
        @param px       pivot x
        @param py       pivot y
    */
    void postRotate(float degrees, float px, float py);

    /** Sets Matrix to Matrix constructed from rotating by degrees about pivot point
        (0, 0), multiplied by Matrix.
        This can be thought of as rotating about the origin after applying Matrix.

        Positive degrees rotates clockwise.

        Given:

                     | J K L |                        | c -s 0 |
            Matrix = | M N O |,  R(degrees, px, py) = | s  c 0 |
                     | P Q R |                        | 0  0 1 |

        where

            c  = cos(degrees)
            s  = sin(degrees)

        sets Matrix to:

                                          | c -s dx | | J K L |   | cJ-sM cK-sN cL-sO |
            R(degrees, px, py) * Matrix = | s  c dy | | M N O | = | sJ+cM sK+cN sL+cO |
                                          | 0  0  1 | | P Q R |   |     P     Q     R |

        @param degrees  angle of axes relative to upright axes
    */
    void postRotate(float degrees);

    /** Sets Matrix to Matrix constructed from skewing by (kx, ky) about pivot point
        (px, py), multiplied by Matrix.
        This can be thought of as skewing about a pivot point after applying Matrix.

        Given:

                     | J K L |                       |  1 kx dx |
            Matrix = | M N O |,  K(kx, ky, px, py) = | ky  1 dy |
                     | P Q R |                       |  0  0  1 |

        where

            dx = -kx * py
            dy = -ky * px

        sets Matrix to:

                                         | 1 kx dx| |J K L|   |J+kx*M+dx*P K+kx*N+dx*Q L+kx*O+dx+R|
            K(kx, ky, px, py) * Matrix = |ky  1 dy| |M N O| = |ky*J+M+dy*P ky*K+N+dy*Q ky*L+O+dy*R|
                                         | 0  0  1| |P Q R|   |          P           Q           R|

        @param kx  horizontal skew factor
        @param ky  vertical skew factor
        @param px  pivot x
        @param py  pivot y
    */
    void postSkew(float kx, float ky, float px, float py);

    /** Sets Matrix to Matrix constructed from skewing by (kx, ky) about pivot point
        (0, 0), multiplied by Matrix.
        This can be thought of as skewing about the origin after applying Matrix.

        Given:

                     | J K L |               |  1 kx 0 |
            Matrix = | M N O |,  K(kx, ky) = | ky  1 0 |
                     | P Q R |               |  0  0 1 |

        sets Matrix to:

                                 |  1 kx 0 | | J K L |   | J+kx*M K+kx*N L+kx*O |
            K(kx, ky) * Matrix = | ky  1 0 | | M N O | = | ky*J+M ky*K+N ky*L+O |
                                 |  0  0 1 | | P Q R |   |      P      Q      R |

        @param kx  horizontal skew factor
        @param ky  vertical skew factor
    */
    void postSkew(float kx, float ky);

    /** Sets Matrix to Matrix other multiplied by Matrix.
        This can be thought of mapping by other after applying Matrix.

        Given:

                     | J K L |           | A B C |
            Matrix = | M N O |,  other = | D E F |
                     | P Q R |           | G H I |

        sets Matrix to:

                             | A B C |   | J K L |   | AJ+BM+CP AK+BN+CQ AL+BO+CR |
            other * Matrix = | D E F | * | M N O | = | DJ+EM+FP DK+EN+FQ DL+EO+FR |
                             | G H I |   | P Q R |   | GJ+HM+IP GK+HN+IQ GL+HO+IR |

        @param other  Matrix on left side of multiply expression
    */
    void postConcat(const Matrix& other);

    /** \enum Matrix::ScaleToFit
        ScaleToFit describes how Matrix is constructed to map one Rect to another.
        ScaleToFit may allow Matrix to have unequal horizontal and vertical scaling,
        or may restrict Matrix to square scaling. If restricted, ScaleToFit specifies
        how Matrix maps to the side or center of the destination Rect.
    */
    enum ScaleToFit {
        kFill_ScaleToFit,   //!< scales in x and y to fill destination Rect
        kStart_ScaleToFit,  //!< scales and aligns to left and top
        kCenter_ScaleToFit, //!< scales and aligns to center
        kEnd_ScaleToFit,    //!< scales and aligns to right and bottom
    };

    /** Sets Matrix to scale and translate src Rect to dst Rect. stf selects whether
        mapping completely fills dst or preserves the aspect ratio, and how to align
        src within dst. Returns false if src is empty, and sets Matrix to identity.
        Returns true if dst is empty, and sets Matrix to:

            | 0 0 0 |
            | 0 0 0 |
            | 0 0 1 |

        @param src  Rect to map from
        @param dst  Rect to map to
        @param stf  one of: kFill_ScaleToFit, kStart_ScaleToFit,
                    kCenter_ScaleToFit, kEnd_ScaleToFit
        @return     true if Matrix can represent Rect mapping
    */
    bool setRectToRect(const Rect& src, const Rect& dst, ScaleToFit stf);

    /** Returns Matrix set to scale and translate src Rect to dst Rect. stf selects
        whether mapping completely fills dst or preserves the aspect ratio, and how to
        align src within dst. Returns the identity Matrix if src is empty. If dst is
        empty, returns Matrix set to:

            | 0 0 0 |
            | 0 0 0 |
            | 0 0 1 |

        @param src  Rect to map from
        @param dst  Rect to map to
        @param stf  one of: kFill_ScaleToFit, kStart_ScaleToFit,
                    kCenter_ScaleToFit, kEnd_ScaleToFit
        @return     Matrix mapping src to dst
    */
    static Matrix MakeRectToRect(const Rect& src, const Rect& dst, ScaleToFit stf) {
        Matrix m;
        m.setRectToRect(src, dst, stf);
        return m;
    }

    /** Sets Matrix to map src to dst. count must be zero or greater, and four or less.

        If count is zero, sets Matrix to identity and returns true.
        If count is one, sets Matrix to translate and returns true.
        If count is two or more, sets Matrix to map Point if possible; returns false
        if Matrix cannot be constructed. If count is four, Matrix may include
        perspective.

        @param src    Point to map from
        @param dst    Point to map to
        @param count  number of Point in src and dst
        @return       true if Matrix was constructed successfully
    */
    bool setPolyToPoly(const Point src[], const Point dst[], int count);

    /** Sets inverse to reciprocal matrix, returning true if Matrix can be inverted.
        Geometrically, if Matrix maps from source to destination, inverse Matrix
        maps from destination to source. If Matrix can not be inverted, inverse is
        unchanged.

        @param inverse  storage for inverted Matrix; may be nullptr
        @return         true if Matrix can be inverted
    */
    bool invert(Matrix* inverse) const {
        // Allow the trivial case to be inlined.
        if (this->isIdentity()) {
            if (inverse) {
                inverse->reset();
            }
            return true;
        }
        return this->invertNonIdentity(inverse);
    }

    /** Fills affine with identity values in column major order.
        Sets affine to:

            | 1 0 0 |
            | 0 1 0 |

        Affine 3x2 matrices in column major order are used by OpenGL and XPS.

        @param affine  storage for 3x2 affine matrix
    */
    static void SetAffineIdentity(float affine[6]);

    /** Fills affine in column major order. Sets affine to:

            | scale-x  skew-x translate-x |
            | skew-y  scale-y translate-y |

        If Matrix contains perspective, returns false and leaves affine unchanged.

        @param affine  storage for 3x2 affine matrix; may be nullptr
        @return        true if Matrix does not contain perspective
    */
    bool asAffine(float affine[6]) const;

    /** Sets Matrix to affine values, passed in column major order. Given affine,
        column, then row, as:

            | scale-x  skew-x translate-x |
            |  skew-y scale-y translate-y |

        Matrix is set, row, then column, to:

            | scale-x  skew-x translate-x |
            |  skew-y scale-y translate-y |
            |       0       0           1 |

        @param affine  3x2 affine matrix
    */
    void setAffine(const float affine[6]);

    /** Maps src Point array of length count to dst Point array of equal or greater
        length. Point are mapped by multiplying each Point by Matrix. Given:

                     | A B C |        | x |
            Matrix = | D E F |,  pt = | y |
                     | G H I |        | 1 |

        where

            for (i = 0; i < count; ++i) {
                x = src[i].fX
                y = src[i].fY
            }

        each dst Point is computed as:

                          |A B C| |x|                               Ax+By+C   Dx+Ey+F
            Matrix * pt = |D E F| |y| = |Ax+By+C Dx+Ey+F Gx+Hy+I| = ------- , -------
                          |G H I| |1|                               Gx+Hy+I   Gx+Hy+I

        src and dst may point to the same storage.

        @param dst    storage for mapped Point
        @param src    Point to transform
        @param count  number of Point to transform
    */
    void mapPoints(Point dst[], const Point src[], int count) const {
        MNN_ASSERT((dst && src && count > 0) || 0 == count);
        // no partial overlap
        MNN_ASSERT(src == dst || &dst[count] <= &src[0] || &src[count] <= &dst[0]);
        this->getMapPtsProc()(*this, dst, src, count);
    }

    /** Maps pts Point array of length count in place. Point are mapped by multiplying
        each Point by Matrix. Given:

                     | A B C |        | x |
            Matrix = | D E F |,  pt = | y |
                     | G H I |        | 1 |

        where

            for (i = 0; i < count; ++i) {
                x = pts[i].fX
                y = pts[i].fY
            }

        each resulting pts Point is computed as:

                          |A B C| |x|                               Ax+By+C   Dx+Ey+F
            Matrix * pt = |D E F| |y| = |Ax+By+C Dx+Ey+F Gx+Hy+I| = ------- , -------
                          |G H I| |1|                               Gx+Hy+I   Gx+Hy+I

        @param pts    storage for mapped Point
        @param count  number of Point to transform
    */
    void mapPoints(Point pts[], int count) const {
        this->mapPoints(pts, pts, count);
    }

    /** Maps Point (x, y) to result. Point is mapped by multiplying by Matrix. Given:

                     | A B C |        | x |
            Matrix = | D E F |,  pt = | y |
                     | G H I |        | 1 |

        result is computed as:

                          |A B C| |x|                               Ax+By+C   Dx+Ey+F
            Matrix * pt = |D E F| |y| = |Ax+By+C Dx+Ey+F Gx+Hy+I| = ------- , -------
                          |G H I| |1|                               Gx+Hy+I   Gx+Hy+I

        @param x       x-axis value of Point to map
        @param y       y-axis value of Point to map
        @param result  storage for mapped Point
    */
    void mapXY(float x, float y, Point* result) const {
        this->getMapXYProc()(*this, x, y, result);
    }

    /** Returns Point (x, y) multiplied by Matrix. Given:

                     | A B C |        | x |
            Matrix = | D E F |,  pt = | y |
                     | G H I |        | 1 |

        result is computed as:

                          |A B C| |x|                               Ax+By+C   Dx+Ey+F
            Matrix * pt = |D E F| |y| = |Ax+By+C Dx+Ey+F Gx+Hy+I| = ------- , -------
                          |G H I| |1|                               Gx+Hy+I   Gx+Hy+I

        @param x  x-axis value of Point to map
        @param y  y-axis value of Point to map
        @return   mapped Point
    */
    Point mapXY(float x, float y) const {
        Point result;
        this->getMapXYProc()(*this, x, y, &result);
        return result;
    }

    /** Sets dst to bounds of src corners mapped by Matrix.
        Returns true if mapped corners are dst corners.

        Returned value is the same as calling rectStaysRect().

        @param dst  storage for bounds of mapped Point
        @param src  Rect to map
        @return     true if dst is equivalent to mapped src
    */
    bool mapRect(Rect* dst, const Rect& src) const;

    /** Sets rect to bounds of rect corners mapped by Matrix.
        Returns true if mapped corners are computed rect corners.

        Returned value is the same as calling rectStaysRect().

        @param rect  rectangle to map, and storage for bounds of mapped corners
        @return      true if result is equivalent to mapped src
    */
    bool mapRect(Rect* rect) const {
        return this->mapRect(rect, *rect);
    }

    /** Returns bounds of src corners mapped by Matrix.

        @param src  rectangle to map
        @return     mapped bounds
    */
    Rect mapRect(const Rect& src) const {
        Rect dst;
        (void)this->mapRect(&dst, src);
        return dst;
    }

    /** Sets dst to bounds of src corners mapped by Matrix. If matrix contains
        elements other than scale or translate: asserts if SK_DEBUG is defined;
        otherwise, results are undefined.

        @param dst  storage for bounds of mapped Point
        @param src  Rect to map
    */
    void mapRectScaleTranslate(Rect* dst, const Rect& src) const;

    /** Returns true if Matrix equals m, using an efficient comparison.

        Returns false when the sign of zero values is the different; when one
        matrix has positive zero value and the other has negative zero value.

        Returns true even when both Matrix contain NaN.

        NaN never equals any value, including itself. To improve performance, NaN values
        are treated as bit patterns that are equal if their bit patterns are equal.

        @param m  Matrix to compare
        @return   true if m and Matrix are represented by identical bit patterns
    */
    bool cheapEqualTo(const Matrix& m) const {
        return 0 == memcmp(fMat, m.fMat, sizeof(fMat));
    }

    /** Compares a and b; returns true if a and b are numerically equal. Returns true
        even if sign of zero values are different. Returns false if either Matrix
        contains NaN, even if the other Matrix also contains NaN.

        @param a  Matrix to compare
        @param b  Matrix to compare
        @return   true if Matrix a and Matrix b are numerically equal
    */
    friend MNN_PUBLIC bool operator==(const Matrix& a, const Matrix& b);

    /** Compares a and b; returns true if a and b are not numerically equal. Returns false
        even if sign of zero values are different. Returns true if either Matrix
        contains NaN, even if the other Matrix also contains NaN.

        @param a  Matrix to compare
        @param b  Matrix to compare
        @return   true if Matrix a and Matrix b are numerically not equal
    */
    friend MNN_PUBLIC bool operator!=(const Matrix& a, const Matrix& b) {
        return !(a == b);
    }

    /** Writes text representation of Matrix to standard output. Floating point values
        are written with limited precision; it may not be possible to reconstruct
        original Matrix from output.
    */
    void dump() const;

    /** Returns the minimum scaling factor of Matrix by decomposing the scaling and
        skewing elements.
        Returns -1 if scale factor overflows or Matrix contains perspective.

        @return  minimum scale factor
    */
    float getMinScale() const;

    /** Returns the maximum scaling factor of Matrix by decomposing the scaling and
        skewing elements.
        Returns -1 if scale factor overflows or Matrix contains perspective.

        @return  maximum scale factor
    */
    float getMaxScale() const;

    /** Sets scaleFactors[0] to the minimum scaling factor, and scaleFactors[1] to the
        maximum scaling factor. Scaling factors are computed by decomposing
        the Matrix scaling and skewing elements.

        Returns true if scaleFactors are found; otherwise, returns false and sets
        scaleFactors to undefined values.

        @param scaleFactors  storage for minimum and maximum scale factors
        @return              true if scale factors were computed correctly
    */
    bool getMinMaxScales(float scaleFactors[2]) const;

    /** Returns reference to const identity Matrix. Returned Matrix is set to:

            | 1 0 0 |
            | 0 1 0 |
            | 0 0 1 |

        @return  const identity Matrix
    */
    static const Matrix& I();

    /** Returns reference to a const Matrix with invalid values. Returned Matrix is set
        to:

            | SK_ScalarMax SK_ScalarMax SK_ScalarMax |
            | SK_ScalarMax SK_ScalarMax SK_ScalarMax |
            | SK_ScalarMax SK_ScalarMax SK_ScalarMax |

        @return  const invalid Matrix
    */
    static const Matrix& InvalidMatrix();

    /** Returns Matrix a multiplied by Matrix b.

        Given:

                | A B C |      | J K L |
            a = | D E F |, b = | M N O |
                | G H I |      | P Q R |

        sets Matrix to:

                    | A B C |   | J K L |   | AJ+BM+CP AK+BN+CQ AL+BO+CR |
            a * b = | D E F | * | M N O | = | DJ+EM+FP DK+EN+FQ DL+EO+FR |
                    | G H I |   | P Q R |   | GJ+HM+IP GK+HN+IQ GL+HO+IR |

        @param a  Matrix on left side of multiply expression
        @param b  Matrix on right side of multiply expression
        @return   Matrix computed from a times b
    */
    static Matrix Concat(const Matrix& a, const Matrix& b) {
        Matrix result;
        result.setConcat(a, b);
        return result;
    }

    /** Sets internal cache to unknown state. Use to force update after repeated
        modifications to Matrix element reference returned by operator[](int index).
    */
    void dirtyMatrixTypeCache() {
        this->setTypeMask(kUnknown_Mask);
    }

    /** Initializes Matrix with scale and translate elements.

            | sx  0 tx |
            |  0 sy ty |
            |  0  0  1 |

        @param sx  horizontal scale factor to store
        @param sy  vertical scale factor to store
        @param tx  horizontal translation to store
        @param ty  vertical translation to store
    */
    void setScaleTranslate(float sx, float sy, float tx, float ty) {
        fMat[kMScaleX] = sx;
        fMat[kMSkewX]  = 0;
        fMat[kMTransX] = tx;

        fMat[kMSkewY]  = 0;
        fMat[kMScaleY] = sy;
        fMat[kMTransY] = ty;

        fMat[kMPersp0] = 0;
        fMat[kMPersp1] = 0;
        fMat[kMPersp2] = 1;

        unsigned mask = 0;
        if (sx != 1 || sy != 1) {
            mask |= kScale_Mask;
        }
        if (tx || ty) {
            mask |= kTranslate_Mask;
        }
        this->setTypeMask(mask | kRectStaysRect_Mask);
    }

    /** Returns true if all elements of the matrix are finite. Returns false if any
        element is infinity, or NaN.

        @return  true if matrix has only finite elements
    */

private:
    /** Set if the matrix will map a rectangle to another rectangle. This
        can be true if the matrix is scale-only, or rotates a multiple of
        90 degrees.

        This bit will be set on identity matrices
    */
    static constexpr int kRectStaysRect_Mask = 0x10;

    /** Set if the perspective bit is valid even though the rest of
        the matrix is Unknown.
    */
    static constexpr int kOnlyPerspectiveValid_Mask = 0x40;

    static constexpr int kUnknown_Mask = 0x80;

    static constexpr int kORableMasks = kTranslate_Mask | kScale_Mask | kAffine_Mask | kPerspective_Mask;

    static constexpr int kAllMasks =
        kTranslate_Mask | kScale_Mask | kAffine_Mask | kPerspective_Mask | kRectStaysRect_Mask;

    float fMat[9];
    mutable uint32_t fTypeMask;

    static void ComputeInv(float dst[9], const float src[9], double invDet, bool isPersp);

    uint8_t computeTypeMask() const;
    uint8_t computePerspectiveTypeMask() const;

    void setTypeMask(int mask) {
        // allow kUnknown or a valid mask
        MNN_ASSERT(kUnknown_Mask == mask || (mask & kAllMasks) == mask ||
                   ((kUnknown_Mask | kOnlyPerspectiveValid_Mask) & mask) ==
                       (kUnknown_Mask | kOnlyPerspectiveValid_Mask));
        fTypeMask = (uint8_t)(mask);
    }

    void orTypeMask(int mask) {
        MNN_ASSERT((mask & kORableMasks) == mask);
        fTypeMask = (uint8_t)(fTypeMask | mask);
    }

    void clearTypeMask(int mask) {
        // only allow a valid mask
        MNN_ASSERT((mask & kAllMasks) == mask);
        fTypeMask = fTypeMask & ~mask;
    }

    TypeMask getPerspectiveTypeMaskOnly() const {
        if ((fTypeMask & kUnknown_Mask) && !(fTypeMask & kOnlyPerspectiveValid_Mask)) {
            fTypeMask = this->computePerspectiveTypeMask();
        }
        return (TypeMask)(fTypeMask & 0xF);
    }

    /** Returns true if we already know that the matrix is identity;
        false otherwise.
    */
    bool isTriviallyIdentity() const {
        if (fTypeMask & kUnknown_Mask) {
            return false;
        }
        return ((fTypeMask & 0xF) == 0);
    }

    inline void updateTranslateMask() {
        if ((fMat[kMTransX] != 0) | (fMat[kMTransY] != 0)) {
            fTypeMask |= kTranslate_Mask;
        } else {
            fTypeMask &= ~kTranslate_Mask;
        }
    }

    typedef void (*MapXYProc)(const Matrix& mat, float x, float y, Point* result);

    static MapXYProc GetMapXYProc(TypeMask mask) {
        MNN_ASSERT((mask & ~kAllMasks) == 0);
        return gMapXYProcs[mask & kAllMasks];
    }

    MapXYProc getMapXYProc() const {
        return GetMapXYProc(this->getType());
    }

    typedef void (*MapPtsProc)(const Matrix& mat, Point dst[], const Point src[], int count);

    static MapPtsProc GetMapPtsProc(TypeMask mask) {
        MNN_ASSERT((mask & ~kAllMasks) == 0);
        return gMapPtsProcs[mask & kAllMasks];
    }

    MapPtsProc getMapPtsProc() const {
        return GetMapPtsProc(this->getType());
    }

    bool invertNonIdentity(Matrix* inverse) const;

    static void Identity_xy(const Matrix&, float, float, Point*);
    static void Trans_xy(const Matrix&, float, float, Point*);
    static void Scale_xy(const Matrix&, float, float, Point*);
    static void ScaleTrans_xy(const Matrix&, float, float, Point*);
    static void Rot_xy(const Matrix&, float, float, Point*);
    static void RotTrans_xy(const Matrix&, float, float, Point*);
    static void Persp_xy(const Matrix&, float, float, Point*);

    static const MapXYProc gMapXYProcs[];

    static void Identity_pts(const Matrix&, Point[], const Point[], int);
    static void Trans_pts(const Matrix&, Point dst[], const Point[], int);
    static void Scale_pts(const Matrix&, Point dst[], const Point[], int);
    static void ScaleTrans_pts(const Matrix&, Point dst[], const Point[], int count);
    static void Persp_pts(const Matrix&, Point dst[], const Point[], int);

    static void Affine_vpts(const Matrix&, Point dst[], const Point[], int);

    static const MapPtsProc gMapPtsProcs[];
    static bool Poly2Proc(const Point srcPt[], Matrix* dst);
    static bool Poly3Proc(const Point srcPt[], Matrix* dst);
    static bool Poly4Proc(const Point srcPt[], Matrix* dst);
};
} // namespace CV
} // namespace MNN
#endif
