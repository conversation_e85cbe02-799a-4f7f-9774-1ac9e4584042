import time
from kubernetes import client, config
from kubernetes.client.rest import ApiException


def get_node_cpu_usage(node_name):
    config.load_kube_config()
    metrics_api = client.CustomObjectsApi()
    core_v1 = client.CoreV1Api()

    try:
        # 获取节点总 CPU 核数（转换为纳核）
        node = core_v1.read_node(node_name)
        cpu_total = int(node.status.capacity["cpu"]) * 10**9  # 1 core = 10^9 nancores

        # 获取当前 CPU 使用量（纳核）
        metrics = metrics_api.list_cluster_custom_object(
            "metrics.k8s.io", "v1beta1", "nodes"
        )
        for item in metrics["items"]:
            if item["metadata"]["name"] == node_name:
                cpu_usage = int(item["usage"]["cpu"].rstrip("n"))
                return min(100, (cpu_usage / cpu_total) * 100)  # 真实利用率

        return 0
    except Exception as e:
        print(f"获取指标失败: {e}")
        return 0

def migrate_pods_from_overloaded_nodes():
    config.load_kube_config()
    v1 = client.CoreV1Api()
    apps_v1 = client.AppsV1Api()

    # 1. 获取所有节点
    nodes = v1.list_node()
    print("\n\n")
    print("====================================================================")
    print("节点状态概览:")
    print("====================================================================")
    print("节点列表:", [node.metadata.name for node in nodes.items])

    for node in nodes.items:
        node_name = node.metadata.name
        if node_name == "zs-asus-tuf-gaming-f16-fx607jv-fx607jv":
            continue
        cpu_usage = get_node_cpu_usage(node_name)
        print(f"节点 {node.metadata.name}")
        print(f"节点 {node.metadata.name} CPU 使用率: {cpu_usage}%")

        
        pods = v1.list_pod_for_all_namespaces(field_selector=f"spec.nodeName={node_name}")
        pod_names = [pod.metadata.name for pod in pods.items if "calico" not in pod.metadata.name and "kube-proxy" not in pod.metadata.name and "coredns" not in pod.metadata.name]
        
        # 格式化输出
        # print(f"节点: {node_name} [CPU利用率: {cpu_usage:.2f}%]")
        print(f"  Pod列表: {pod_names}")
        print("-" * 40)


        new_cpu_limit = 1.5
        migrate_flag = False
        if node.metadata.name == "yds-desktop" and cpu_usage > 80 and not migrate_flag:
        # if node.metadata.name == "yds-desktop" and False:
            target_node_selector = {"computing_device": "compute_node3"}
            print(f"迁移节点 {node.metadata.name} 的 Pod processor-service")
            pods = v1.list_pod_for_all_namespaces(field_selector=f"spec.nodeName={node.metadata.name}")
            for pod in pods.items:
                if "processor-service" in pod.metadata.name:
                    print(f"开始迁移 Pod: {pod.metadata.name} (Namespace: {pod.metadata.namespace})")
                    namespace = pod.metadata.namespace
                    # 获取 Pod 所属的 Deployment
                    owner_refs = pod.metadata.owner_references
                    if not owner_refs:
                        print("该 Pod 没有控制器，不能自动修改 Deployment")
                        continue

                    for owner in owner_refs:
                        if owner.kind == "ReplicaSet":
                            # 找到对应的 ReplicaSet
                            replicaset = apps_v1.read_namespaced_replica_set(owner.name, namespace)
                            # 获取 Deployment 名称
                            for rs_owner in replicaset.metadata.owner_references:
                                if rs_owner.kind == "Deployment":
                                    deployment_name = rs_owner.name
                                    print(f"对应的 Deployment 名称为: {deployment_name}")

                                    # 获取 Deployment
                                    deployment = apps_v1.read_namespaced_deployment(deployment_name, namespace)

                                    # 修改 nodeSelector
                                    deployment.spec.template.spec.node_selector = target_node_selector

                                    # 修改 CPU 限制
                                    for container in deployment.spec.template.spec.containers:
                                        if container.name == "processor":
                                            if not container.resources:
                                                container.resources = client.V1ResourceRequirements()
                                            if not container.resources.limits:
                                                container.resources.limits = {}
                                            container.resources.limits["cpu"] = new_cpu_limit

                                    # 应用更改
                                    apps_v1.patch_namespaced_deployment(
                                        name=deployment_name,
                                        namespace=namespace,
                                        body=deployment
                                    )
                                    migrate_flag = True
                                    print(f"Pod {pod.metadata.name} 已迁移到 {target_node_selector}")

                                    break

                    

migrate_pods_from_overloaded_nodes()

while True:
    migrate_pods_from_overloaded_nodes()
    time.sleep(5)